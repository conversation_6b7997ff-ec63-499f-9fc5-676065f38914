<!DOCTYPE html>
<html>
<head>
<title>20250711-ups_wxd_custom_crawler_deploy_runbook_html.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="custom-web-crawler-deployment-instructions-for-an-ibm-watsonx-discovery-wxd-instance-at-united-parcel-service-inc-ups">Custom Web Crawler Deployment Instructions for an IBM watsonx Discovery (wxD) Instance at United Parcel Service, Inc. (UPS)</h1>
<p>[v0.0.2 Aug 13, 2025]</p>
<table>
<thead>
<tr>
<th><strong>DISCLAIMER</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>This document is a supplement to, and not a replacement for, official documentation. Although the information in this document has been assembled to the best of the authors' (contributors') knowledge, we disclaim any liability with the future use of this information. The owners of the solution must delegate responsibility to the appropriate teams so that documentation can maintain its recency. Please refer to official documentation and/or contact your IBM representative for clarifications and/or additional information.</strong></td>
</tr>
<tr>
<td><strong>This document only covers the deployment of Custom Crawler resources to an OpenShift cluster to make the application available for use.</strong></td>
</tr>
<tr>
<td><strong>CUSTOM CRAWLER</strong>:<br><br><strong>THE CUSTOM CRAWLER AND ALL ITS CODE (THE OPEN-SOURCE BASE CODE AND ANY CUSTOM/MODIFIED CODE) COME WITHOUT WARRANTY OF ANY KIND. USE AT YOUR OWN RISK. THE AUTHOR(S) AND IBM ASSUME NO RESPONSIBILITY IN ANY WAY FOR ERRORS, ISSUES, AND/OR PROBLEMS CAUSED BY THE EXECUTION OF THIS APPLICATION NOR DO THEY ASSUME OWNERSHIP, LICENSE, AND/OR SUPPORT IN THE USE OF THE CODE INCLUDED IN THE APPLICATION. THE INFORMATION CONTAINED IN THE APPLICATION IS PROVIDED ON AN &quot;AS IS&quot; BASIS AND MAINTENANCE, FIXES, AND UPDATES ARE THE RESPONSIBILITY OF THE INHERITED PARTIES.</strong></td>
</tr>
</tbody>
</table>
<h1 id="overview">Overview</h1>
<p>Elasticsearch (ES)/IBM watsonx Discovery (wxD) comes with a feature that allows users to deploy Elastic Crawler (a web crawler) instances. To view deployed instances or to access the page that allows users to create a new one, follow these steps:</p>
<ol>
<li>
<p>From the <code>Home</code> page, click the navigation menu (three horizontal lines in the top-left area of the page) and click the <code>Content</code> link that can be foudn in the <code>Search</code> expandable section [of the menu].</p>
</li>
<li>
<p>After the <code>Elasticsearch Indices</code> page finishes loading, find the <code>Web crawlers</code> link on the left side of the page (navigation menu) and click it.</p>
</li>
<li>
<p>This should load the <code>Elasticsearch web crawlers</code> page.</p>
</li>
</ol>
<p>Unfortuantely, the Elastic Crawler has too many limitations to be used by the UPS Search Component Development Team and a custom crawler had to be developed - which will be referred to as, &quot;Custom Crawler&quot;. The key factors driving this decision are:</p>
<ol>
<li>Crawl configs cannot be exported/imported from/to other environments. This would increase configuration management complexity and add to the risk of configuration drift across environments.</li>
<li>Elasticsearch doesn't provide any features that enable version control capabilities for crawler configuration(s). This would increase configuration management complexity.</li>
<li>Not all crawl configuration fields required by UPS are supported OOTB. This doesn't meet UPS' functional requirements and would decrease performance of the proposed solution (i.e. <code>no-follow</code> and <code>no-index</code> are not supported).</li>
</ol>
<p>The Custom Crawler incorporates and extends code originally derived from the following open-source projects (with modifications):</p>
<ul>
<li>
<p><a href="https://github.com/scrapy/scrapyd">scrapy/scrapyd</a></p>
<p>Used as a reference for how to write the custom crawler code for UPS.</p>
</li>
<li>
<p><a href="https://github.com/my8100/scrapydweb">my8100/scrapydweb</a></p>
<p>Used to generate the UI for the custome crawler. Significant modification was adding SAML SSO to the UI.</p>
</li>
</ul>
<p>The Custom Crawler application has three important components:</p>
<ul>
<li>Crawler python code.</li>
<li>Scrapyd server.</li>
<li>Scrapydweb UI.</li>
</ul>
<h2 id="environment-and-activity-description">Environment and Activity Description</h2>
<p>The tasks in this document are written to be performed/executed from the bastion host of the target OpenShift Container Platform (OCP) cluster, unless stated otherwise.</p>
<blockquote>
<p><strong>NOTE</strong>: UPS is using OpenShift Dedicated (OSD) 4.17.x with Workload Identity Federation (WIF) on the Google Cloud Platform (GCP) which is a managed-OCP offering provided by Red Hat, Inc. UPS has deployed three private OSD clusters; one non-production and two production (active-active). These clusters, and the bastion host, have public internet access (egress) but only select OSD applications are exposed for public access (ingress) via custom OCP Ingress Controllers.</p>
</blockquote>
<h1 id="deploying-the-custom-crawler">Deploying the Custom Crawler</h1>
<p>Deploying an instance of the Custom Crawler, on a &quot;fresh&quot; OCP clsuter, includes the following activities:</p>
<ol>
<li>Push the image to the private registry.</li>
<li>Deploy an instance of the crawler for a specific wxD instance.</li>
<li>Execute post-deployment configuration tasks.</li>
</ol>
<h2 id="pre-requisites">Pre-requisites</h2>
<ol>
<li>
<p>Bastion host and admin OSD cluster access.</p>
</li>
<li>
<p>Private registry read/write access.</p>
</li>
<li>
<p>The cluster must be configured to pull images from the target private registry that will host the crawler image.</p>
<blockquote>
<p><strong>NOTE</strong>: The OSD clusters will be pulling Custom Crawler image(s) from the <strong>Google Artifact Registry (GAR)</strong>.</p>
</blockquote>
</li>
<li>
<p>A built crawler image has been uploaded to <a href="https://ibm.ent.box.com/folder/330198143682">custom_crawler_images</a> (full path: <code>External Client Working Area - 'WEX Replacement ups.com Search' use case</code> -&gt; <code>Artifacts</code> -&gt; <code>custom_crawler_images</code>) IBM/UPS Box folder.</p>
<p>If the image was built on an local machine, then run the following command to save the image as a compressed file before uploading to Box:</p>
<pre><code> podman save --format docker-archive -o wxd-ingest-crawler_v&lt;version_number&gt;.tar &lt;image_id&gt;
</code></pre>
</li>
<li>
<p>Known instance routes (hostnames) and ingress IPs (for DNS record and firewall rule creation, wherever applicable).</p>
</li>
<li>
<p>TLS certificate, TLS key (unencrypted), intermediate certificate(s), and root CA certificate (PEM format).</p>
<blockquote>
<p><strong>NOTE</strong>: The certificate file <strong>MUST</strong> contain the full chain of certificates; with the server's certificate first, followed by any intermediate certificates, adn finally the root Certificate Authority (CA) certificate.</p>
</blockquote>
</li>
<li>
<p>Prepared deployment YAML files:</p>
<ul>
<li><code>ingest-config-cm.yaml</code> (ingest config file)</li>
<li><code>ingest-saml-cm.yaml</code> (SAML config file for SSO)</li>
<li><code>ingest-svc.yaml</code> (service definition file to expose OCP StatefulSet internally)</li>
<li><code>ingest-sts.yaml</code> (Custom Crawler application specification/configuration)</li>
</ul>
</li>
<li>
<p>An OSD cluster with the following programs/instances already deployed:</p>
<ul>
<li>watsonx Discovery (specifically Elasticsearch)
<ul>
<li>
<p>The instance must contain an index named <code>spider-configs</code> that contains configurations for every crawler Python Class defined in the Custom Crawler image.</p>
<blockquote>
<p><strong>NOTE</strong>: Consult Custom Crawler documentation that was provided by the Delivery Team.</p>
<ul>
<li>UPSers [and IBMers] can find this in various README files within the latest code that was shared [on July 15 2025] via Box: <a href="https://ibm.ent.box.com/file/1799993629446?s=********************************">wxd-ingest-dev.zip</a>. <strong>UPS is responsible for maintaining the documentation as crawler code is modified.</strong></li>
</ul>
<p>IBMers can access the internal git repo used to generate the shared zip file: <a href="https://github.ibm.com/TEL-UPS/wxd-ingest">TEL-UPS/wxd-ingest</a></p>
</blockquote>
</li>
<li>
<p>(Optional) A dedicated user and password configured for the Custom Crawler application.</p>
</li>
</ul>
</li>
<li>watsonx.ai
<ul>
<li>At least one LLM model deployed. The model that is defined in the Custom Crawler's <code>PDF_DESCRIPTION_MODEL</code> environment variable must be deployed in the target watsonx.ai instance.</li>
</ul>
</li>
</ul>
</li>
<li>
<p>For each crawler instance, the following information must be known so that environment variables can be created (in the <code>wxd-api-secret</code>):</p>
<table>
<thead>
<tr>
<th>Variable</th>
<th>Sample Value</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td>ELASTICSEARCH_URL</td>
<td>https://wx-discovery.ccca.ams1907.com</td>
<td>This is the Elasticsearch cluster that the crawler is interacting with.</td>
</tr>
<tr>
<td>ELASTICSEARCH_USER_NAME</td>
<td>ingestservice</td>
<td>This is the user name that identifies the crawler instance.</td>
</tr>
<tr>
<td>ELASTICSEARCH_PASSWORD</td>
<td>49o8hgrbiaro9m*poe-123</td>
<td>This is the password that the crawler uses to authenticate to the Elasticsearch cluster.</td>
</tr>
<tr>
<td>SCRAPYDWEB_PASSWORD</td>
<td>RthnngsU6JxB2ptoSTbk</td>
<td>This establishes a way for the application to authenticate itself when running scheduled jobs.</td>
</tr>
<tr>
<td>WATSONX_BASE_URL</td>
<td>https://cpd.ccca.ams1907.com/ml/v1</td>
<td>PDF ingestion includes a pre-processing step that uses a watsonx.ai LLM to generate a document description that is used as the document description in each PDF document record of the respective index.</td>
</tr>
<tr>
<td>WATSONX_API_TOKEN</td>
<td>&lt;really_long_string&gt;</td>
<td>Authentication...</td>
</tr>
<tr>
<td>WATSONX_PROJECT_ID</td>
<td>3c917cf9-1fbb-4327-8974-db247673f3c1</td>
<td>This is a required parameter when making API calls to interact with an LLM dpeloyed on the watsonx.ai platform.</td>
</tr>
</tbody>
</table>
</li>
<li>
<p>The application, its users, and their respective groups/roles registered in Azure Entra ID with the UPS Identity Management Group/Team.</p>
</li>
</ol>
<h2 id="push-the-image-to-the-private-registry">Push the image to the private registry</h2>
<ol>
<li>
<p>Upload the saved crawler image to the bastion host.</p>
</li>
<li>
<p>Load the saved image to the local container storage directory (using either rootful or rootless podman).</p>
<pre><code> podman load -i wxd-ingest-crawler_v&lt;version_number&gt;.tar
</code></pre>
</li>
<li>
<p>Tag the loaded image.</p>
<blockquote>
<p><strong>NOTE</strong>: Deployments will be strictly controlled by image versions. Tag all images using a version number and never use the <code>latest</code> tag.</p>
</blockquote>
</li>
<li>
<p>Log into the GAR repository.</p>
</li>
<li>
<p>Push the image to the GAR repository.</p>
</li>
</ol>
<h2 id="deploy-an-instance-of-the-crawler-for-a-specific-wxd-instance">Deploy an instance of the crawler for a specific wxD instance</h2>
<p>A dedicated crawler is deployed for every instance of wxD. Each crawler is deployed into a dedicated OCP project that uses the following naming convention: <code>&lt;wxd_ns&gt;-ingest</code>. In other words, the new project name uses the project name of its paired wxD instance and appends the <code>-ingest</code> suffix to it.</p>
<ol>
<li>
<p>Create the Custom Crawler instance project.</p>
<pre><code> oc new-project &lt;wxd_ns&gt;-ingest
</code></pre>
</li>
<li>
<p>Retrieve the project's uid-range and use the default id to set the value for the <code>.spec.template.spec.containers[ingestwui].securityContext.fsGroup</code> field in the <code>ingest-sts.yaml</code> file</p>
<pre><code> oc get namespace &lt;wxd_ns&gt;-ingest \
 -o jsonpath='{.metadata.annotations.openshift\.io/sa\.scc\.uid-range}' | cut -d'/' -f1
</code></pre>
</li>
<li>
<p>Create the <code>ingest-config</code> ConfigMap.</p>
<pre><code> oc apply -f ingest-config-cm.yaml -n &lt;wxd_ns&gt;-ingest
</code></pre>
</li>
<li>
<p>Create the <code>saml-configmap</code> ConfigMap.</p>
<pre><code> oc apply -f ingest-saml-cm.yaml -n &lt;wxd_ns&gt;-ingest
</code></pre>
</li>
<li>
<p>Create the TLS Secret.</p>
<pre><code> oc create secret tls &lt;tls_secret_name&gt; \
 --cert=&lt;fullchain_certificate&gt;.crt \
 --key=&lt;tls&gt;.key \
 -n &lt;wxd_ns&gt;-ingest
</code></pre>
</li>
<li>
<p>Create the <code>wxd-api-secret</code> Secret.</p>
<pre><code> oc create secret generic wxd-api-secret \
 --from-literal=ELASTICSEARCH_URL=&lt;value1&gt; \
 --from-literal=ELASTICSEARCH_USER_NAME=&lt;value2&gt; \
 --from-literal=ELASTICSEARCH_PASSWORD=&lt;value3&gt; \
 --from-literal=SCRAPYDWEB_PASSWORD=&lt;value4&gt; \
 --from-literal=WATSONX_BASE_URL=&lt;value5&gt; \
 --from-literal=WATSONX_API_TOKEN=&lt;value6&gt; \
 --from-literal=WATSONX_PROJECT_ID=&lt;value7&gt; \
 -n &lt;wxd_ns&gt;-ingest
</code></pre>
<p>Example:</p>
<pre><code> oc create secret generic wxd-api-secret \
 --from-literal=ELASTICSEARCH_URL=https://wx-discovery.ccca.ams1907.com \
 --from-literal=ELASTICSEARCH_USER_NAME=ingestservice \
 --from-literal=ELASTICSEARCH_PASSWORD=49o8hgrbiaro9m*poe-123 \
 --from-literal=SCRAPYDWEB_PASSWORD=RthnngsU6JxB2ptoSTbk \
 --from-literal=WATSONX_BASE_URL=https://cpd.ccca.ams1907.com/ml/v1 \
 --from-literal=WATSONX_API_TOKEN=&lt;really_long_string&gt; \
 --from-literal=WATSONX_PROJECT_ID=3c917cf9-1fbb-4327-8974-db247673f3c1 \
 -n ups-wxd-dev-ingest
</code></pre>
</li>
<li>
<p>Create the Custom Crawler StatefulSet and watch the pod status for errors.</p>
<pre><code> oc apply -f ingest-sts.yaml
</code></pre>
</li>
<li>
<p>Create the Custom Crawler Service.</p>
<pre><code> oc apply -f ingest-svc.yaml
</code></pre>
</li>
<li>
<p>Create the Custom Crawler Route using the the predetermined hostname. Patch the route with TLS specs. Add the appropriate label to the route so that traffic is routed correctly.</p>
<p>a. Expose the service.</p>
<pre><code> oc expose svc &lt;service_name&gt; \
 --name &lt;route_name&gt; \
 --hostname &lt;hostname_found_in_tls_cert_or_SAN&gt; \
 -n &lt;wxd_ns&gt;-ingest
</code></pre>
<p>b. Patch the route with TLS specs.</p>
<pre><code> oc patch route &lt;route_name&gt; -n &lt;wxd_ns&gt;-ingest \
 --type=merge \
 -p '{&quot;spec&quot;:{&quot;tls&quot;:{&quot;termination&quot;:&quot;passthrough&quot;}}}'
</code></pre>
<p>c. Label the route (if traffic will be routed through the non-default Ingess Controller).</p>
<pre><code> oc label route &lt;route_name&gt; &lt;label_key&gt;=&lt;label_value&gt; -n &lt;wxd_ns&gt;-ingest
</code></pre>
</li>
</ol>
<h1 id="supplemental-guidance">Supplemental Guidance</h1>
<p>This section has the following additional guidance that is related to the continued use of the product:</p>
<ul>
<li>Replacing TLS Certificates</li>
<li>Updating the Deployed Custom Crawler Image Version</li>
<li>Changing Custom Crawler Instance Host Aliases (In Non-Prod)</li>
<li>Overriding the Default Request Headers Used for Web Scraping</li>
<li>Overriding Other Default Environment Variables</li>
</ul>
<h2 id="replacing-tls-certificates">Replacing TLS Certificates</h2>
<p>To avoid breaks in trust, certificates should be rotated before expiration.</p>
<blockquote>
<p><strong>NOTE</strong>: The wxD and Custom Crawler instances use a SAN certificate for each environment, non-prod and prod, to simplify certificate management. As a result, certificates for wxD and Custom Crawler instances will done in tandem.</p>
</blockquote>
<ol>
<li>
<p>Obtain a new TLS certificate and key (and other certificates in the chain, if appliacable).</p>
</li>
<li>
<p>Create a full chain certificate in PEM format (if it doesn't exist, already).</p>
<blockquote>
<p><strong>NOTE</strong>: Make sure that the certificate contains the certificate in the correct order; starting with the server's certificate first, followed by any intermediate certificates, and finally the root Certificate Authority (CA) certificate.</p>
</blockquote>
</li>
<li>
<p>Create a new tls secret in the Custom Crawler instance OSD project.</p>
</li>
<li>
<p>Update the <code>ingest-sts.yaml</code> file, replacing the old secret name value in <code>spec.template.spec.volumes[name:&lt;env&gt;-scrapydweb-tls-volume].secret.secretName</code> with the new secret name.</p>
</li>
<li>
<p>Apply the new yaml file [in the appropriate OSD project] and watch the pod status for errors.</p>
<blockquote>
<p><strong>NOTE</strong>: This will restart the stateful set pod. Make sure to apply the change when there are <strong>NO ACTIVE JOBS</strong> in the pod.</p>
</blockquote>
</li>
<li>
<p>Share the new certificate with the Entra ID team for their corresponding registered application(s).</p>
</li>
</ol>
<h2 id="updating-the-deployed-custom-crawler-image-version">Updating the Deployed Custom Crawler Image Version</h2>
<p>As new requirements surface, the Custom Crawler image version might need to be updated. If a newer image version needs to be deployed, then following these steps.</p>
<ol>
<li>
<p>Build and/or push the new container image to the appropriate location in the Google Artifact Registry.</p>
<blockquote>
<p><strong>NOTE</strong>: Deployments will be strictly controlled by image versions. Tag all images using a version number and never use the <code>latest</code> tag.</p>
</blockquote>
</li>
<li>
<p>Update the <code>.spec.template.spec.contaienrs[name: ingestwui].image</code> field in the <code>ingest-sts.yaml</code> file.</p>
<blockquote>
<p><strong>NOTE</strong>: Deployments will be strictly controlled by image versions. Tag all images using a version number and never use the <code>latest</code> tag.</p>
</blockquote>
</li>
<li>
<p>Apply the new yaml file [in the appropriate OSD project] and watch the pod status for errors.</p>
<blockquote>
<p><strong>NOTE</strong>: This will restart the stateful set pod. Make sure to apply the change when there are <strong>NO ACTIVE JOBS</strong> in the pod.</p>
</blockquote>
</li>
</ol>
<h2 id="changing-custom-crawler-instance-host-aliases-in-non-prod">Changing Custom Crawler Instance Host Aliases (In Non-Prod)</h2>
<p>Non-production Custom Crawler instances are configured to 'crawl'/scrape content from private UPS servers. For some use cases, UPS developers access private environments by modifying the hosts file on their local machines. Host aliases for the Custom Crawlers are used to produce a similar behavior.</p>
<p>If hosts information needs to chagned/updated, then simply modify the entries in the <code>.spec.template.spec.hostAliases</code> section of the <code>ingest-sts.yaml</code> file.</p>
<h2 id="overriding-the-default-request-headers-used-for-web-scraping">Overriding the Default Request Headers Used for Web Scraping</h2>
<p>During the initial delivery engagement, the development teams experienced the following problems, which required the modification of the original HTTP request headers.</p>
<ul>
<li>
<p>PROBLEM: Akamai throttling/blocking Custom Crawler content scraping requests.</p>
<p>RESOLUTION: IBM Delivery, UPS Search, and UPS Akamai Teams (Lionel Andre) agreed to use a custom header, called <code>'X-UPS-WatsonSearch'</code>, and value to identify the Custom Crawler as an approved agent for scraping content from Akamai hosted/delivered pages.</p>
</li>
<li>
<p>PROBLEM: UPS servers and/or proxies sending brotli-encoded responses that made crawler logs show the following error&quot; <code>brotli.error: BrotliDecoderDecompressStream failed while processing the stream</code>, preventing content extraction.</p>
<p>RESOLUTION: Explicitly define the values for the <code>'Accept-Encoding'</code> header for HTTP scraping requests and exclude the <code>'br'</code> (Brotli) encoding value from the list. Current key and values:</p>
<p><code>'Accept-Encoding': 'gzip, deflate'</code></p>
</li>
</ul>
<p>The default HTTP request headers shouldn't need to be changed, but in case they do, new headers can be 'injected' using a Config Map (avoiding the nuisance of having to build a new container image for such a change).</p>
<p>To override the default crawler request headers, perform the following steps:</p>
<ol>
<li>
<p>Create a crawler-headers-config ConfigMap in the appropriate Custom Crawler OSD project.</p>
</li>
<li>
<p>Update the <code>.spec.template.spec.containers[name: ingestwui].env[name: CRAWLER_HEADERS]</code> section in the <code>ingest-sts.yaml</code> file, specifically removing the <code>optional: true</code> line nested under <code>.valueFrom.configMapKeyRef</code> while making sure that the name matches the name of the ConfigMap that was recently created.</p>
</li>
<li>
<p>Apply the new yaml file [in the appropriate OSD project] and watch the pod status for errors.</p>
<blockquote>
<p><strong>NOTE</strong>: This will restart the stateful set pod. Make sure to apply the change when there are <strong>NO ACTIVE JOBS</strong> in the pod.</p>
</blockquote>
</li>
</ol>
<h2 id="overriding-other-default-environment-variables">Overriding Other Default Environment Variables</h2>
<p>Other environment variables can be modified similarly to what has already been described in the above section. It is recommended that a knowledgeable individual be assigned the planning and implementation of such requests.</p>
<p>Some environment variables that might need to be changed in the future:</p>
<ul>
<li><code>PDF_DESCRIPTION_MODEL</code> (Default: <code>ibm/granite-3-2-8b-instruct</code> - This value can be obtained from the watsonx.ai UI.)</li>
<li><code>PDF_INDEX</code> (Default: <code>true</code> - Boolean flag that instructs the crawler to store PDF documents in a separate index with a <code>-pdf</code> suffix.</li>
</ul>
<h1 id="deployment-variation-deploying-w-basic-auth-instead-of-saml-sso">Deployment Variation: Deploying w/ BASIC AUTH instead of SAML SSO</h1>
<p>If a Custom Crawler instance has to be deployed before an application can be registered with the Azure Entra ID team, then the application can be deploying using a temporary admin user (until SAML SSO can be configured). To do so, perform the same deployment steps as described above, with the following exceptions:</p>
<ol>
<li>
<p>Do not create the <code>saml-configmap</code>.</p>
</li>
<li>
<p>Edit the <code>ingest-sts.yaml</code> file, removing references to the saml confimap.</p>
</li>
<li>
<p>Create the <code>wxd-api-secret</code> with the following environment variables (adding one new environment variable):</p>
<table>
<thead>
<tr>
<th>Variable</th>
<th>Sample Value</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td>ELASTICSEARCH_URL</td>
<td>https://wx-discovery.ccca.ams1907.com</td>
<td>This is the Elasticsearch cluster that the crawler is interacting with.</td>
</tr>
<tr>
<td>ELASTICSEARCH_USER_NAME</td>
<td>ingestservice</td>
<td>This is the user name that identifies the crawler instance.</td>
</tr>
<tr>
<td>ELASTICSEARCH_PASSWORD</td>
<td>49o8hgrbiaro9m*poe-123</td>
<td>This is the password that the crawler uses to authenticate to the Elasticsearch cluster.</td>
</tr>
<tr>
<td>SCRAPYDWEB_PASSWORD</td>
<td>RthnngsU6JxB2ptoSTbk</td>
<td>This establishes a way for the application to authenticate itself when running scheduled jobs.</td>
</tr>
<tr>
<td>WATSONX_BASE_URL</td>
<td>https://cpd.ccca.ams1907.com/ml/v1</td>
<td>PDF ingestion includes a pre-processing step that uses a watsonx.ai LLM to generate a document description that is used as the document description in each PDF document record of the respective index.</td>
</tr>
<tr>
<td>WATSONX_API_TOKEN</td>
<td>&lt;really_long_string&gt;</td>
<td>Authentication...</td>
</tr>
<tr>
<td>WATSONX_PROJECT_ID</td>
<td>3c917cf9-1fbb-4327-8974-db247673f3c1</td>
<td>This is a required parameter when making API calls to interact with an LLM dpeloyed on the watsonx.ai platform.</td>
</tr>
<tr>
<td>SCRAPYDWEB_ENABLE_AUTH</td>
<td>true</td>
<td>Enables basic authentication on the UI.</td>
</tr>
</tbody>
</table>
<p>Example:</p>
<pre><code> oc create secret generic wxd-api-secret \
 --from-literal=ELASTICSEARCH_URL=https://wx-discovery.ccca.ams1907.com \
 --from-literal=ELASTICSEARCH_USER_NAME=ingestservice \
 --from-literal=ELASTICSEARCH_PASSWORD=49o8hgrbiaro9m*poe-123 \
 --from-literal=SCRAPYDWEB_PASSWORD=RthnngsU6JxB2ptoSTbk \
 --from-literal=WATSONX_BASE_URL=https://cpd.ccca.ams1907.com/ml/v1 \
 --from-literal=WATSONX_API_TOKEN=&lt;really_long_string&gt; \
 --from-literal=WATSONX_PROJECT_ID=3c917cf9-1fbb-4327-8974-db247673f3c1 \
 --from-literal=SCRAPYDWEB_ENABLE_AUTH=true \
 -n ups-wxd-dev-ingest
</code></pre>
<blockquote>
<p><strong>NOTE</strong>: The default username for login is <code>admin</code>. If that needs to be chagned, then add one more variable to the <code>wxd-api-secret</code> secret by assigning the desired username string to the <code>SCRAPYDWEB_USER</code> key.</p>
</blockquote>
<blockquote>
<p>Example additional parameter:</p>
</blockquote>
<blockquote>
<pre><code>  --from-literal=SCRAPYDWEB_USER=crawlerAdmin
</code></pre>
</blockquote>
</li>
</ol>
<h1 id="appendix-pre-requisite-yaml-files-reference">Appendix: Pre-requisite YAML Files Reference</h1>
<p><strong>Consult Custom Crawler docs for information for advanced configurations/settings.</strong></p>
<h2 id="ingest-config-cmyaml-configmap-example"><code>ingest-config-cm.yaml</code> (ConfigMap) Example</h2>
<pre><code>apiVersion: v1
data:
  CONFIGS_BASEPATH: /config
  SCRAPYDWEB_ENABLE_HTTPS: &quot;true&quot;
  SCRAPYDWEB_CERTIFICATE_FILEPATH: &quot;/certs/tls.crt&quot;
  SCRAPYDWEB_PRIVATEKEY_FILEPATH: &quot;/certs/tls.key&quot;
  SCRAPYDWEB_DATA_PATH: &quot;/scrapyd/scrapydweb_data&quot;
kind: ConfigMap
metadata:
  name: ingest-config
  namespace: ups-wxd-dev-ingest
</code></pre>
<p>This file will likely only need to have the namespace field changed for each deployment.</p>
<h2 id="ingest-saml-cmyaml-configmap-example"><code>ingest-saml-cm.yaml</code> (ConfigMap) Example</h2>
<pre><code>apiVersion: v1
kind: ConfigMap
metadata:
  name: saml-configmap
  namespace: ups-wxd-dev-ingest
data:
  SCRAPYDWEB_ENABLE_SAML: &quot;true&quot;
  SCRAPYDWEB_SAML_CONFIG_PATH: &quot;/saml&quot;
  SCRAPYDWEB_SAML_SESSSION_TIMEOUT: &quot;60&quot;
  SCRAPYDWEB_SAML_GROUP_ATTRIBUTE_NAME: &quot;http://schemas.microsoft.com/ws/2008/06/identity/claims/role&quot;
  SCRAPYDWEB_SAML_ALLOWED_GROUPS: &quot;DevAdmin&quot;
  advanced_settings.json: |
    {
        &quot;security&quot;: {
            &quot;requestedAuthnContext&quot;: false,
            &quot;failOnAuthnContextMismatch&quot;: false
        }
    }
  settings.json: |
    {
        &quot;strict&quot;: false,
        &quot;debug&quot;: true,
        &quot;sp&quot;: {
          &quot;entityId&quot;: &quot;https://ingest-dev-ui.ccca.ams1907.com&quot;,
          &quot;assertionConsumerService&quot;: {
            &quot;url&quot;: &quot;https://ingest-dev-ui.ccca.ams1907.com/acs/&quot;,
            &quot;binding&quot;: &quot;urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST&quot;
          },
          &quot;singleLogoutService&quot;: {
            &quot;url&quot;: &quot;https://ingest-dev-ui.ccca.ams1907.com/sls/&quot;,
            &quot;binding&quot;: &quot;urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect&quot;
          },
          &quot;NameIDFormat&quot;: &quot;urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified&quot;
        },
        &quot;idp&quot;: {
          &quot;entityId&quot;: &quot;https://sts.windows.net/e7520e4d-d5a0-488d-9e9f-949faae7dce8/&quot;,
          &quot;singleSignOnService&quot;: {
            &quot;url&quot;: &quot;https://login.microsoftonline.com/e7520e4d-d5a0-488d-9e9f-949faae7dce8/saml2&quot;,
            &quot;binding&quot;: &quot;urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect&quot;
          },
          &quot;singleLogoutService&quot;: {
            &quot;url&quot;: &quot;https://login.microsoftonline.com/e7520e4d-d5a0-488d-9e9f-949faae7dce8/saml2&quot;,
            &quot;binding&quot;: &quot;urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect&quot;
          },
          &quot;x509cert&quot;: &quot;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&quot;
        }
      }
</code></pre>
<p>Edit contents of this file accordingly.</p>
<h2 id="ingest-stsyaml-statefulset-example"><code>ingest-sts.yaml</code> (StatefulSet) Example</h2>
<pre><code>apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
  name: ingest-dev
  namespace: ups-wxd-dev-ingest
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ingest-sts
  serviceName: ingest-dev
  template:
    metadata:
      labels:
        app: ingest-sts
    spec:
      containers:
      - envFrom:
        - secretRef:
            name: wxd-api-secret
        - configMapRef:
            name: ingest-config
        - configMapRef:
            name: saml-configmap
        env:
        - name: CRAWLER_HEADERS
          valueFrom:
            configMapKeyRef:
              name: crawler-headers-config
              key: CRAWLER_HEADERS
              optional: true
        image: us-docker.pkg.dev/gcp-dct-ccca-dev/ccca-d-image-registry/wex-replacement/ingestwui:1.2.8
        imagePullPolicy: Always
        name: ingestwui
        ports:
        - containerPort: 5000
          protocol: TCP
        volumeMounts:
        - mountPath: /scrapyd/
          name: scrapyd-storage
        - mountPath: /certs
          name: dev-scrapydweb-tls-volume
          readOnly: true
        - mountPath: /saml
          name: saml-config-volume
          readOnly: true
      dnsPolicy: ClusterFirst
      hostAliases:
      - hostnames:
        - www.ups.com
        - ups.com
        ip: **************
      - hostnames:
        - wwwapps.ups.com
        - apps.ups.com
        - developer.ups.com
        ip: *************
      - hostnames:
        - campusship.ups.com
        - www.campusship.ups.com
        ip: **************
      - hostnames:
        - wwwcie.ups.com
        - www.cie.ups.com
        - cie.ups.com
        ip: **************
      - hostnames:
        - filexfer.ups.com
        - www.filexfer.ups.com
        ip: **************
      - hostnames:
        - webservices.ups.com
        - www.webservices.ups.com
        ip: **************
      - hostnames:
        - onlinetools.ups.com
        - www.onlinetools.ups.com
        ip: *************
      - hostnames:
        - www.livesite.ups.com
        - livesite.ups.com
        ip: *************
      - hostnames:
        - webapis.ups.com
        ip: **************
      - hostnames:
        - es-us.ups.com
        - es-us-m.ups.com
        - es-us-apps.ups.com
        - es-us-campusship.ups.com
        - si.ups.com
        - si-m.ups.com
        - si-apps.ups.com
        - si-campusship.ups.com
        - si-filexfer.ups.com
        - ua.ups.com
        - ua-m.ups.com
        - ua-apps.ups.com
        - ua-campusship.ups.com
        - ua-filexfer.ups.com
        - ru.ups.com
        - ru-m.ups.com
        - ru-apps.ups.com
        - ru-campusship.ups.com
        - ru-filexfer.ups.com
        ip: *************
      initContainers:
      - command:
        - /bin/sh
        - -c
        - mkdir -p /scrapyd/logs /scrapyd/dbs /scrapyd/items /certs
        - |
          echo &quot;Checking for TLS Certificates...&quot;
          if [ ! -f /certs/tls.crt ] || [ ! -f /certs/tls.key ]; then
            echo &quot;TLS certificate or key not found. Exiting...&quot;
            exit 1
          fi
          echo &quot;TLS certificates exist. Proceeding...&quot;
          exit 0
        image: us-docker.pkg.dev/gcp-dct-ccca-dev/ccca-d-image-registry/wex-replacement/busybox
        imagePullPolicy: Always
        name: init-scrapyd
        volumeMounts:
        - mountPath: /scrapyd/
          name: scrapyd-storage
        - mountPath: /certs
          name: dev-scrapydweb-tls-volume
          readOnly: true
      restartPolicy: Always
      securityContext:
        fsGroup: 1001320000
      volumes:
      - name: dev-scrapydweb-tls-volume
        secret:
          defaultMode: 420
          secretName: fullchain-tls-20260701
      - configMap:
          defaultMode: 420
          items:
          - key: settings.json
            path: settings.json
          - key: advanced_settings.json
            path: advanced_settings.json
          name: saml-configmap
        name: saml-config-volume
  volumeClaimTemplates:
  - apiVersion: v1
    kind: PersistentVolumeClaim
    metadata:
      name: scrapyd-storage
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 64Gi
      storageClassName: ssd-csi
</code></pre>
<h2 id="ingest-svcyaml-service-example"><code>ingest-svc.yaml</code> (Service) Example</h2>
<pre><code>apiVersion: v1
kind: Service
metadata:
  name: ingest-dev
  namespace: ups-wxd-dev-ingest
spec:
  ports:
  - port: 5000
    protocol: TCP
    targetPort: 5000
  selector:
    app: ingest-sts
  type: ClusterIP
</code></pre>
<h2 id="ingest-customheaders-cmyaml-configmap-example"><code>ingest-custom_headers-cm.yaml</code> (ConfigMap) Example</h2>
<pre><code>apiVersion: v1
kind: ConfigMap
metadata:
  name: crawler-headers-config
  namespace: ups-wxd-dev-ingest
data:
  CRAWLER_HEADERS: &gt;
    {
      &quot;Accept-Encoding&quot;: &quot;gzip, deflate&quot;,
      &quot;accept&quot;: &quot;text/html,application/xhtml+xml,application/xml&quot;,
      &quot;accept-language&quot;: &quot;en-US,en;q=0.9,de;q=0.8&quot;,
      &quot;cache-control&quot;: &quot;max-age=0&quot;,
      &quot;priority&quot;: &quot;u=0, i&quot;,
      &quot;sec-ch-ua&quot;: &quot;Not(A:Brand\\\&quot;;v=\\\&quot;99\\\&quot;, \\\&quot;Google Chrome\\\&quot;;v=\\\&quot;133\\\&quot;, \\\&quot;Chromium\\\&quot;;v=\\\&quot;133&quot;,
      &quot;sec-ch-ua-mobile&quot;: &quot;?0&quot;,
      &quot;sec-ch-ua-platform&quot;: &quot;macOS&quot;,
      &quot;sec-fetch-dest&quot;: &quot;document&quot;,
      &quot;sec-fetch-mode&quot;: &quot;navigate&quot;,
      &quot;sec-fetch-site&quot;: &quot;none&quot;,
      &quot;sec-fetch-user&quot;: &quot;?1&quot;,
      &quot;upgrade-insecure-requests&quot;: &quot;1&quot;,
      &quot;user-agent&quot;: &quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36&quot;,
      &quot;X-UPS-WatsonSearch&quot;: &quot;V2F0c29uIFNlYXJjaCBzY3JhcGVy&quot;
    }
</code></pre>
<h1 id="document-history">DOCUMENT HISTORY</h1>
<table>
<thead>
<tr>
<th>VERSION</th>
<th>DATE</th>
<th>COMMENT</th>
</tr>
</thead>
<tbody>
<tr>
<td>v.0.0.1</td>
<td>Aug 12, 2025</td>
<td>Initial release, submitted for internal review by Essential Management.</td>
</tr>
<tr>
<td>v.0.0.2</td>
<td>Aug 13, 2025</td>
<td>Added links for crawler code that was shared with UPS, submitted for internal review by Essential Management.</td>
</tr>
</tbody>
</table>

</body>
</html>
