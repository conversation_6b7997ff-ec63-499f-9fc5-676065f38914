#!/usr/bin/env python3
"""
Test script for local file processing functionality.

This script creates sample HTML and PDF files and tests the local file processing capabilities.
"""

import os
import tempfile
import shutil
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_html_file(file_path: str, title: str = "Test Document", content: str = "This is test content."):
    """Create a test HTML file."""
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Test HTML document for Lumen ingestion">
    <meta name="keywords" content="test, lumen, html">
    <meta name="locale" content="en_US">
    <title>{title}</title>
</head>
<body>
    <main>
        <h1>{title}</h1>
        <p>{content}</p>
        <p>This document was created for testing the Lumen local file processing capabilities.</p>
    </main>
</body>
</html>"""
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"Created test HTML file: {file_path}")

def create_test_directory_structure(base_dir: str):
    """Create a test directory structure with sample files."""
    
    # Create subdirectories
    docs_dir = os.path.join(base_dir, "documents")
    guides_dir = os.path.join(base_dir, "guides")
    os.makedirs(docs_dir, exist_ok=True)
    os.makedirs(guides_dir, exist_ok=True)
    
    # Create HTML files
    create_test_html_file(
        os.path.join(base_dir, "index.html"),
        "Lumen Documentation Home",
        "Welcome to the Lumen documentation portal."
    )
    
    create_test_html_file(
        os.path.join(docs_dir, "getting-started.html"),
        "Getting Started Guide",
        "This guide will help you get started with Lumen services."
    )
    
    create_test_html_file(
        os.path.join(guides_dir, "api-reference.html"),
        "API Reference",
        "Complete API reference for Lumen services."
    )
    
    # Create a simple text file that should be ignored
    with open(os.path.join(base_dir, "readme.txt"), 'w') as f:
        f.write("This is a text file that should be ignored by the processor.")
    
    logger.info(f"Created test directory structure in: {base_dir}")
    return {
        'base_dir': base_dir,
        'html_files': [
            os.path.join(base_dir, "index.html"),
            os.path.join(docs_dir, "getting-started.html"),
            os.path.join(guides_dir, "api-reference.html")
        ],
        'ignored_files': [
            os.path.join(base_dir, "readme.txt")
        ]
    }

def test_local_html_extractor():
    """Test the LocalHtmlExtractor."""
    try:
        from wxdingest.ingest.local_html_extractor import LocalHtmlExtractor
        
        # Create a temporary HTML file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            create_test_html_file(f.name, "Test HTML Extraction", "Testing HTML extraction functionality.")
            temp_file = f.name
        
        try:
            # Test extraction
            extractor = LocalHtmlExtractor()
            doc = extractor.extract(temp_file, base_url="https://lumen.test")
            
            if doc:
                logger.info("✓ LocalHtmlExtractor test passed")
                logger.info(f"  Title: {doc.title}")
                logger.info(f"  URL: {doc.url}")
                logger.info(f"  Locale: {doc.locale}")
                logger.info(f"  Body length: {len(doc.body)} characters")
                return True
            else:
                logger.error("✗ LocalHtmlExtractor test failed - no document returned")
                return False
                
        finally:
            os.unlink(temp_file)
            
    except Exception as e:
        logger.error(f"✗ LocalHtmlExtractor test failed: {e}")
        return False

def test_local_file_controller():
    """Test the LocalFileIngestController with a temporary directory."""
    try:
        from wxdingest.ingest.local_file_ingest_controller import LocalFileIngestController
        
        # Create temporary directory with test files
        temp_dir = tempfile.mkdtemp()
        try:
            test_structure = create_test_directory_structure(temp_dir)
            
            # Initialize controller (this might fail if WatsonX Discovery is not configured)
            try:
                controller = LocalFileIngestController()
                logger.info("✓ LocalFileIngestController initialized successfully")
            except Exception as e:
                logger.warning(f"⚠ Could not initialize controller (WxD not configured): {e}")
                logger.info("✓ LocalFileIngestController import test passed")
                return True
            
            # Test file discovery
            files = controller._find_files(temp_dir, recursive=True)
            expected_files = len(test_structure['html_files'])
            
            if len(files) == expected_files:
                logger.info(f"✓ File discovery test passed - found {len(files)} files")
                for file_path in files:
                    logger.info(f"  Found: {file_path}")
                return True
            else:
                logger.error(f"✗ File discovery test failed - expected {expected_files}, found {len(files)}")
                return False
                
        finally:
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        logger.error(f"✗ LocalFileIngestController test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting local file processing tests...")
    
    tests = [
        ("LocalHtmlExtractor", test_local_html_extractor),
        ("LocalFileIngestController", test_local_file_controller),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        if test_func():
            passed += 1
        else:
            logger.error(f"Test {test_name} failed")
    
    logger.info(f"\n=== Test Results ===")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("✓ All tests passed!")
        return 0
    else:
        logger.error("✗ Some tests failed")
        return 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
