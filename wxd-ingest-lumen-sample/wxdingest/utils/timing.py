
import time
import uuid

from wxdingest.utils.applog import get_logger
logger = get_logger(__name__)
        
class Timing:
    """
    Utility function to capture runtime of different
    parts of the code. It measures the time and outputs
    the runtime when stop is called. It can track multiple
    and nested processes at the same time.
    
    Usage:
    
    tid = timing.start('Name of process')
    timing.stop(tid)
    """
    
    def __init__(self) -> None:
        self.start_time = None
        self.starts = {}
        self.min_diff = 0
    
    def start(self, name):
        uid = str(uuid.uuid4())
        self.starts[uid] = (name, time.time())
        return uid
        
    def stop(self, uid:str):
        start_entry = self.starts[uid]
        if start_entry is not None:
            diff = int(time.time() - start_entry[1])
            # if diff > self.min_diff:
            #     logger.info(f"TIMING --- {start_entry[0]} took {diff} seconds")
            
            logger.info(f"TIMING --- {start_entry[0]} took {time.time() - start_entry[1]} seconds")
        self.start_time = None
        return diff
        
    def next(self, uid:str, name:str):
        self.stop(uid)
        uid = self.start(name)
        return uid