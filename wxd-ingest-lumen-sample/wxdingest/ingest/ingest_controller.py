
import os
from scrapy.crawler import CrawlerProcess
from scrapy.settings import Settings
from scrapy.utils.project import get_project_settings
import json
from typing import Dict, List
import logging
logger = logging.getLogger(__name__)

from wxdingest.model import SpiderConfig
from wxdingest.ingest.template import Template<PERSON>pider
from wxdingest.ingest.local_file_ingest_controller import LocalFileIngestController


class IngestController:
    """
    Kicks off the crawler process that is coordinated
    by the scrapy library. It makes sure to load a spider
    (or crawler) that is focused on the specified configuration.
    
    
    TODO: Allow for configuring instance name
    """

    def __init__(self):
        self.CONFIGS_BASEPATH = 'config/spider_configs/'
        self.SETTINGS_BASEPATH = 'config/spider_settings/'

    def list_config_names(self) -> List[str]:
        """
        Utility function to list the names of all the configs
        that have been specified. That helps the user to make
        sure that a valid configuration is specified.
        """
        return [os.fsdecode(file).replace('.json', '') for file in os.listdir(os.fsencode(self.CONFIGS_BASEPATH))]

    def ingest(self, config_name: str):
        """
        Loads the various settings and configuration,
        and kicks off the crawler process that crawls
        and indexes documents into Elasticsearch.
        """

        # Load scrapy settings
        settings: Settings = self._load_scrapy_settings()

        # Start crawling
        self._start_crawl(settings, config_name)

    def ingest_local_directory(self, directory_path: str, index: str,
                              pipeline: str = "english", locale_default: str = "en_US",
                              base_url: str = None, recursive: bool = True,
                              force_refresh: bool = False, instance_name: str = None):
        """
        Ingest HTML and PDF files from a local directory.

        Args:
            directory_path: Path to directory containing files
            index: Elasticsearch index name
            pipeline: Processing pipeline name
            locale_default: Default locale if not detected
            base_url: Optional base URL for generating document URLs
            recursive: Whether to process subdirectories
            force_refresh: Whether to force update all documents
            instance_name: WatsonX Discovery instance name

        Returns:
            Dictionary with processing statistics
        """
        controller = LocalFileIngestController(instance_name=instance_name)
        return controller.ingest_directory(
            directory_path=directory_path,
            index=index,
            pipeline=pipeline,
            locale_default=locale_default,
            base_url=base_url,
            recursive=recursive,
            force_refresh=force_refresh
        )

    def ingest_local_file(self, file_path: str, index: str,
                         pipeline: str = "english", locale_default: str = "en_US",
                         base_url: str = None, force_refresh: bool = False,
                         instance_name: str = None):
        """
        Ingest a single HTML or PDF file from local filesystem.

        Args:
            file_path: Path to the file
            index: Elasticsearch index name
            pipeline: Processing pipeline name
            locale_default: Default locale if not detected
            base_url: Optional base URL for generating document URLs
            force_refresh: Whether to force update the document
            instance_name: WatsonX Discovery instance name

        Returns:
            IngestDocument if successful, None if failed
        """
        controller = LocalFileIngestController(instance_name=instance_name)
        return controller.ingest_file(
            file_path=file_path,
            index=index,
            pipeline=pipeline,
            locale_default=locale_default,
            base_url=base_url,
            force_refresh=force_refresh
        )

    def _load_scrapy_settings(self) -> Settings:
        """
        Loads settings.py file that resides in the deploy directory.
        """
        # This is where the settings.py resides that is used both locally
        # and when deployed as a pod.
        settings_file_path:str = 'wxdingest.ingest.deploy.lumen_ingest.settings'
        # Force scrapy to pull the settings file from there
        os.environ.setdefault('SCRAPY_SETTINGS_MODULE', settings_file_path)
        # Pull settings file
        settings:Settings = get_project_settings()
        # When running locally, it doesn't recognize the scrapy project
        # path, so we just remove that piece here since it is not relevant
        # to run it locally.
        settings.delete('SPIDER_MODULES')

        return settings
    
    def _load_crawler_config(self, config_path: str) -> SpiderConfig:
        # Load the crawler configuration
        try:
            with open(config_path) as fin:
                return SpiderConfig.from_json(json.load(fin))
        except Exception as e:
            logger.error(f"ERROR - error loading crawler configuration: {e}")
            return None

    def _start_crawl(self, settings: Settings, config_name:str) -> None:
        """
        Once we loaded the settings and config,
        we can start the crawler. This will run for
        a while.
        """
        c = CrawlerProcess(settings)
        c.crawl(TemplateSpider, config_name=config_name, purge="false", force_refresh="false")
        c.start()

if __name__ == '__main__':
    # Example usage for web crawling
    # IngestController().ingest('lumen-main')

    # Example usage for local file processing
    controller = IngestController()

    # Process a local directory
    # stats = controller.ingest_local_directory(
    #     directory_path='/path/to/local/files',
    #     index='lumen-local-docs',
    #     pipeline='english',
    #     locale_default='en_US',
    #     recursive=True
    # )
    # print(f"Processed {stats['processed']} files")

    # Process a single local file
    # doc = controller.ingest_local_file(
    #     file_path='/path/to/document.pdf',
    #     index='lumen-local-docs'
    # )
    # if doc:
    #     print(f"Processed: {doc.url}")

    # For testing, use the test configuration
    IngestController().ingest('test')
