import os
import hashlib
from datetime import datetime
from bs4 import BeautifulSoup
import html2text
from pathlib import Path
from urllib.parse import urljoin
from urllib.request import pathname2url

import logging
logger = logging.getLogger(__name__)

from wxdingest.model import IngestDocument
from wxdingest.ingest.ingest_utils import IngestUtils


class LocalHtmlExtractor:
    """
    Extracts metadata and body from local HTML files.
    """
    
    def __init__(self):
        self.utils = IngestUtils()

    def extract(self, file_path: str, base_url: str = None) -> IngestDocument:
        """
        Extract content from a local HTML file.
        
        Args:
            file_path: Path to the local HTML file
            base_url: Optional base URL to use for generating document URL
        
        Returns:
            IngestDocument with extracted content
        """
        try:
            # Read the HTML file
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
        except Exception as e:
            logger.warning(f"Could not read HTML file {file_path}: {e}")
            return None
        
        # Create a mock response-like object for compatibility
        mock_response = MockResponse(file_path, html_content, base_url)
        
        doc: IngestDocument = self._extract_document_metadata(mock_response, file_path)
        doc.body = self._extract_body(html_content)
        doc.file_type = "HTML"
        return doc
    
    def _extract_document_metadata(self, mock_response, file_path: str) -> IngestDocument:
        """
        Parses the HTML content to extract metadata fields.
        """
        doc = IngestDocument()
        doc.url = mock_response.url
        doc.id = hashlib.md5(doc.url.encode('utf-8')).hexdigest()
        
        # Parse HTML with BeautifulSoup for metadata extraction
        soup = BeautifulSoup(mock_response.html_content, 'html.parser')
        
        # Extract title
        try:
            title_tag = soup.find('title')
            if title_tag:
                doc.title = title_tag.get_text().strip()
        except Exception as e:
            logger.info(f'Error extracting title from {file_path}: {e}')
            
        # Extract description from meta tag
        try:
            desc_tag = soup.find('meta', attrs={'name': 'description'})
            if desc_tag:
                doc.description = desc_tag.get('content')
        except:
            pass
            
        # Extract keywords from meta tag
        try:
            keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
            if keywords_tag:
                doc.keywords = [kw.strip() for kw in keywords_tag.get('content').split(',')]
        except:
            pass
            
        # Extract locale from meta tag
        try:
            locale_tag = soup.find('meta', attrs={'name': 'locale'}) or \
                        soup.find('meta', attrs={'name': 'lumen-locale'})
            if locale_tag:
                doc.locale = locale_tag.get('content')
        except:
            pass
            
        # If we don't find locale metadata, try to extract from URL/filename
        if doc.locale is None:
            self._extract_locale(doc, None)
            
        # Set published time to file modification time or current time
        try:
            file_stat = os.stat(file_path)
            doc.published_time = datetime.fromtimestamp(file_stat.st_mtime).isoformat() + "Z"
        except:
            doc.published_time = datetime.utcnow().isoformat() + "Z"

        return doc
    
    def _extract_locale(self, doc, locale_default: str = None):
        """Extract locale from URL or use default."""
        # Determine country and language
        country, language = self.utils.get_country_and_language_from_url_and_locale(doc.url, doc.locale)
        # Generate locale (if locale is not provided in metadata)
        if doc.locale is None:
            doc.locale = self.utils.generate_locale(country, language, locale_default)

    @staticmethod
    def _extract_body(html_content: str) -> str:
        """
        Extracts text from HTML content.
        """
        try:
            # Parse HTML with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Try to find main content area first
            main_content = soup.find('main')
            if main_content is None:
                main_content = soup.find('article')
            if main_content is None:
                main_content = soup.find('div', class_=['content', 'main-content', 'body'])
            if main_content is None:
                main_content = soup
            
            # Convert to string for html2text processing
            html_str = str(main_content)
            
            # Use html2text for clean text extraction
            h = html2text.HTML2Text()
            h.ignore_links = True
            h.skip_internal_links = True
            h.inline_links = False
            h.ignore_images = True
            h.ignore_emphasis = True
            h.ignore_mailto_links = True
            return h.handle(html_str)
            
        except Exception as e1:
            # Fallback to BeautifulSoup text extraction
            logger.debug("html2text didn't work. Trying BeautifulSoup for text extraction...")
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                return soup.get_text()
            except Exception as e2:
                logger.warning(f"Could not extract HTML body:\n{e1}\n{e2}")
                return ""


class MockResponse:
    """
    Mock response object to maintain compatibility with existing extractor interface.
    """
    
    def __init__(self, file_path: str, html_content: str, base_url: str = None):
        self.file_path = file_path
        self.html_content = html_content
        
        # Generate URL from file path
        if base_url:
            # Use provided base URL
            relative_path = os.path.relpath(file_path)
            self.url = urljoin(base_url, relative_path.replace(os.sep, '/'))
        else:
            # Convert file path to file:// URL
            path = Path(file_path).resolve()
            self.url = path.as_uri()
    
    def xpath(self, xpath_expr: str):
        """Mock xpath method for compatibility."""
        # This is a simplified implementation for basic xpath expressions
        soup = BeautifulSoup(self.html_content, 'html.parser')
        
        if xpath_expr == '//title/text()':
            title_tag = soup.find('title')
            return MockXPathResult(title_tag.get_text().strip() if title_tag else None)
        elif xpath_expr == '//meta[@name="description"]/@content':
            desc_tag = soup.find('meta', attrs={'name': 'description'})
            return MockXPathResult(desc_tag.get('content') if desc_tag else None)
        elif xpath_expr == '//meta[@name="keywords"]/@content':
            keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
            return MockXPathResult(keywords_tag.get('content') if keywords_tag else None)
        elif xpath_expr == '//meta[@name="locale"]/@content' or xpath_expr == '//meta[@name="lumen-locale"]/@content':
            locale_tag = soup.find('meta', attrs={'name': 'locale'}) or \
                        soup.find('meta', attrs={'name': 'lumen-locale'})
            return MockXPathResult(locale_tag.get('content') if locale_tag else None)
        elif xpath_expr == '//meta[@name="published-time"]/@content':
            pub_tag = soup.find('meta', attrs={'name': 'published-time'})
            return MockXPathResult(pub_tag.get('content') if pub_tag else None)
        elif xpath_expr == '//main':
            main_tag = soup.find('main')
            return MockXPathResult(str(main_tag) if main_tag else None)
        
        return MockXPathResult(None)


class MockXPathResult:
    """Mock XPath result for compatibility."""
    
    def __init__(self, value):
        self.value = value
    
    def get(self):
        return self.value
