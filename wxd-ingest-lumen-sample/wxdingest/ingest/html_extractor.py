
from bs4 import BeautifulSoup
import html2text
import hashlib
from datetime import datetime

import logging
logger = logging.getLogger(__name__)

from wxdingest.model import IngestDocument
from wxdingest.ingest.ingest_utils import IngestUtils


class HtmlExtractor:
    """
    Extracts metadata and body from HTML pages.
    """
    
    def __init__(self):
        self.utils = IngestUtils()

    
    def extract(self, response) -> IngestDocument:
        
        # Tests whether we can extract text from the page. If it is an
        # impage, this will fail
        try:
            text = response.text
        except Exception as e:
            logger.warning(f"Could not parse {response.url}: {e}")
            return None
        
        doc: IngestDocument = self._extract_document_metadata(response)
        doc.body = self._extract_body(response)
        # Set the document type
        doc.file_type = "HTML"
        return doc
    
    
    def _extract_document_metadata(self, response) -> IngestDocument:
        """
        Parses the page HTML to extract the metadata fields
        as well as the body of the page.
        """
        doc = IngestDocument()
        doc.url = response.url
        doc.id = hashlib.md5(doc.url.encode('utf-8')).hexdigest()
        
        # Try to grab the information from the page HTML
        # Initialize fields - they are common across all document parts
        
        try:
            doc.title = response.xpath('//title/text()').get()
        except Exception as e:
            logger.info(f'Error with response Response: ({response.status}) "{response.text}": {e}')
            
        try:
            doc.description = response.xpath(
                '//meta[@name="description"]/@content').get()
        except:
            pass
        try:
            doc.keywords = response.xpath(
                '//meta[@name="keywords"]/@content').get().split(',')
        except:
            pass
        try:
            doc.locale = response.xpath(
                '//meta[@name="locale"]/@content').get()
        except:
            pass
        # Try alternative locale metadata names
        if doc.locale is None:
            try:
                doc.locale = response.xpath(
                    '//meta[@name="lumen-locale"]/@content').get()
            except:
                pass
        # If we don't find locale metadata, we try to compose the locale
        # based on alternative metadata attributes
        if doc.locale is None:
            try:
                country = response.xpath(
                    '//meta[@name="country"]/@content').get()
                language = response.xpath(
                    '//meta[@name="language"]/@content').get()
                if country is not None and language is not None:
                    doc.locale = f"{language.lower()}_{country.upper()}"
            except:
                pass
        
        # If we still don't have a locale, we'll try to extract it from the URL
        if doc.locale is None:
            self._extract_locale(doc,doc.locale)
            
            
        try:
            doc.published_time = response.xpath(
                '//meta[@name="published-time"]/@content').get()
        except:
            pass
        if not doc.published_time:
            doc.published_time = datetime.utcnow().isoformat() + "Z"

        return doc
    
    
    def _extract_locale(self, doc, locale_default:str=None):
        # Determine country and language
        country, language = self.utils.get_country_and_language_from_url_and_locale(doc.url, doc.locale)
        # Generate locale (if locale is not provided in metadata)
        if doc.locale is None:
            doc.locale = self.utils.generate_locale(country, language, locale_default)

    @staticmethod
    def _extract_body(response) -> str:
        """
        Extracts text from HTML.
        """
        
        html: str = response.xpath('//main').get()
        if html is None:
            html = response.body
        
        # First, try to use HTML to text 
        try:
            h = html2text.HTML2Text()
            h.ignore_links = True
            h.skip_internal_links = True
            h.inline_links = False
            h.ignore_images = True
            h.ignore_emphasis = True
            h.ignore_mailto_links = True
            return h.handle(html)
        except Exception as e1:
            # If that doesn't work (which it doesn't in worldship), use
            # a more basic conversion that has historically worked
            logger.debug("HtmlToText didn't work. Trying BeautifulSoup for text extraction...")
            try:
                soup = BeautifulSoup(html, features="lxml")
                return soup.get_text()
            except Exception as e2:
                # Fine. We give up.
                logger.warning(f"Could not extract HTML body:\n{e1}\n{e2}")
                return ""
    