import os
import glob
from pathlib import Path
from typing import List, Dict, Optional
from time import time
from datetime import datetime

import logging
logger = logging.getLogger(__name__)

from wxdingest.model import IngestDocument
from wxdingest.ingest.local_html_extractor import LocalHtmlExtractor
from wxdingest.ingest.local_pdf_extractor import LocalPdfExtractor
from wxdingest.ingest.page_processor import PageProcessor
from wxdingest.elasticsearch_interface import ElasticsearchInterface
from wxdingest.setup.instances_manager import InstancesManager
from wxdingest import config


class LocalFileIngestController:
    """
    Controller for ingesting HTML and PDF files from local file system.
    Maintains compatibility with existing PageProcessor for indexing.
    """
    
    def __init__(self, instance_name: str = None):
        self.html_extractor = LocalHtmlExtractor()
        self.pdf_extractor = LocalPdfExtractor()
        self.processor = PageProcessor(instance_name=instance_name)
        self.instance_name = instance_name or config.get_parameter("INSTANCE_NAME", "dev")
        
        # Supported file extensions
        self.html_extensions = ['.html', '.htm']
        self.pdf_extensions = ['.pdf']
        
    def ingest_directory(self, 
                        directory_path: str, 
                        index: str, 
                        pipeline: str = "english",
                        locale_default: str = "en_US",
                        base_url: str = None,
                        recursive: bool = True,
                        force_refresh: bool = False) -> Dict[str, int]:
        """
        Ingest all HTML and PDF files from a directory.
        
        Args:
            directory_path: Path to directory containing files
            index: Elasticsearch index name
            pipeline: Processing pipeline name
            locale_default: Default locale if not detected
            base_url: Optional base URL for generating document URLs
            recursive: Whether to process subdirectories
            force_refresh: Whether to force update all documents
            
        Returns:
            Dictionary with processing statistics
        """
        if not os.path.exists(directory_path):
            raise ValueError(f"Directory does not exist: {directory_path}")
        
        if not os.path.isdir(directory_path):
            raise ValueError(f"Path is not a directory: {directory_path}")
        
        logger.info(f"Starting local file ingest from directory: {directory_path}")
        logger.info(f"Target index: {index}, Pipeline: {pipeline}")
        
        # Find all supported files
        files_to_process = self._find_files(directory_path, recursive)
        
        logger.info(f"Found {len(files_to_process)} files to process")
        
        # Process statistics
        stats = {
            'total_files': len(files_to_process),
            'processed': 0,
            'errors': 0,
            'html_files': 0,
            'pdf_files': 0,
            'new_documents': 0,
            'updated_documents': 0,
            'skipped_documents': 0
        }
        
        start_time = time()
        
        for file_path in files_to_process:
            try:
                result = self.ingest_file(
                    file_path=file_path,
                    index=index,
                    pipeline=pipeline,
                    locale_default=locale_default,
                    base_url=base_url,
                    force_refresh=force_refresh
                )
                
                if result:
                    stats['processed'] += 1
                    
                    # Update file type counters
                    if result.file_type == 'HTML':
                        stats['html_files'] += 1
                    elif result.file_type == 'PDF':
                        stats['pdf_files'] += 1
                    
                    # Update action counters
                    if result.ingest_action == 'new':
                        stats['new_documents'] += 1
                    elif result.ingest_action == 'update':
                        stats['updated_documents'] += 1
                    else:
                        stats['skipped_documents'] += 1
                        
                else:
                    stats['errors'] += 1
                    
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                stats['errors'] += 1
        
        end_time = time()
        duration = end_time - start_time
        
        logger.info(f"Local file ingest completed in {duration:.2f} seconds")
        logger.info(f"Statistics: {stats}")
        
        return stats
    
    def ingest_file(self, 
                   file_path: str, 
                   index: str, 
                   pipeline: str = "english",
                   locale_default: str = "en_US",
                   base_url: str = None,
                   force_refresh: bool = False) -> Optional[IngestDocument]:
        """
        Ingest a single HTML or PDF file.
        
        Args:
            file_path: Path to the file
            index: Elasticsearch index name
            pipeline: Processing pipeline name
            locale_default: Default locale if not detected
            base_url: Optional base URL for generating document URLs
            force_refresh: Whether to force update the document
            
        Returns:
            IngestDocument if successful, None if failed
        """
        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return None
        
        file_ext = Path(file_path).suffix.lower()
        
        try:
            # Extract document based on file type
            if file_ext in self.html_extensions:
                doc = self.html_extractor.extract(file_path, base_url)
            elif file_ext in self.pdf_extensions:
                # For PDFs, check if we should use separate index
                pdf_index = index
                if self.processor.PDF_INDEX:
                    pdf_index += "-pdf"
                doc = self.pdf_extractor.extract(file_path, base_url)
                if doc:
                    doc.index = pdf_index
            else:
                logger.warning(f"Unsupported file type: {file_path}")
                return None
            
            if doc is None:
                logger.error(f"Failed to extract content from: {file_path}")
                return None
            
            # Create a mock response for compatibility with PageProcessor
            mock_response = MockLocalResponse(file_path, doc)
            
            # Use existing PageProcessor for indexing (maintains all existing logic)
            processed_doc = self.processor.process_page(
                response=mock_response,
                index=index,
                pipeline=pipeline,
                locale_default=locale_default,
                force_refresh=force_refresh
            )
            
            if processed_doc:
                logger.info(f"Successfully processed: {file_path} -> {processed_doc.ingest_action}")
            
            return processed_doc
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return None
    
    def _find_files(self, directory_path: str, recursive: bool = True) -> List[str]:
        """Find all supported files in the directory."""
        files = []
        
        # All supported extensions
        extensions = self.html_extensions + self.pdf_extensions
        
        if recursive:
            # Use glob to find files recursively
            for ext in extensions:
                pattern = os.path.join(directory_path, '**', f'*{ext}')
                files.extend(glob.glob(pattern, recursive=True))
        else:
            # Only look in the immediate directory
            for ext in extensions:
                pattern = os.path.join(directory_path, f'*{ext}')
                files.extend(glob.glob(pattern))
        
        # Sort files for consistent processing order
        return sorted(files)


class MockLocalResponse:
    """
    Mock response object to maintain compatibility with PageProcessor.
    """
    
    def __init__(self, file_path: str, doc: IngestDocument):
        self.file_path = file_path
        self.url = doc.url
        self.doc = doc
        
        # Mock properties for compatibility
        self.status = 200
        self.body = b""  # Not used for local files
        
        # Determine if it's a PDF based on file extension
        self._is_pdf = Path(file_path).suffix.lower() == '.pdf'
    
    @property
    def text(self):
        """Mock text property."""
        return self.doc.body or ""
    
    def xpath(self, xpath_expr: str):
        """Mock xpath method - not used for local files."""
        return MockXPathResult(None)


class MockXPathResult:
    """Mock XPath result for compatibility."""
    
    def __init__(self, value):
        self.value = value
    
    def get(self):
        return self.value
