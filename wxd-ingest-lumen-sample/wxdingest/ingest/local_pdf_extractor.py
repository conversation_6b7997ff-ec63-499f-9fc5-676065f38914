import os
import hashlib
from datetime import datetime
from pypdf import Pdf<PERSON>ead<PERSON>
from pathlib import Path
from urllib.parse import urljoin
from urllib.request import pathname2url

import logging
logger = logging.getLogger(__name__)

from wxdingest.model import IngestDocument
from wxdingest.ingest.ingest_utils import IngestUtils
from wxdingest.ingest.pdf_description_generator import PDFDescriptionGenerator


class LocalPdfExtractor:
    """
    Extracts metadata and body from local PDF files.
    """
    
    def __init__(self):
        self.utils = IngestUtils()
        self.pdf_description_generator = PDFDescriptionGenerator()

    def extract(self, file_path: str, base_url: str = None) -> IngestDocument:
        """
        Extract content from a local PDF file.
        
        Args:
            file_path: Path to the local PDF file
            base_url: Optional base URL to use for generating document URL
        
        Returns:
            IngestDocument with extracted content
        """
        try:
            # Read the PDF file
            with open(file_path, 'rb') as f:
                pdf_content = f.read()
            
            pdf: PdfReader = PdfReader(file_path)
            
            # Create a mock response-like object for compatibility
            mock_response = MockPdfResponse(file_path, pdf_content, base_url)
            
            # Extract metadata
            doc: IngestDocument = self._extract_document_metadata(mock_response, pdf)
            # Extract body text
            doc.body = self._extract_body(pdf)
            # Generate description using AI (if available)
            try:
                doc.description = self.pdf_description_generator.describe_single_pdf(doc)
            except Exception as e:
                logger.warning(f"Could not generate AI description for PDF {file_path}: {e}")
                # Fallback to title or filename
                if not doc.description:
                    doc.description = doc.title or os.path.basename(file_path)
            
            # Set the document type
            doc.file_type = "PDF"
            return doc
            
        except Exception as e:
            logger.warning(f"Could not parse PDF {file_path}: {e}")
            return None
    
    def _extract_body(self, pdf: PdfReader) -> str:
        """Extract text content from PDF pages."""
        try:
            return "\n".join([
                p.extract_text(extraction_mode="plain", layout_mode_space_vertically=False) 
                for p in pdf.pages
            ])
        except Exception as e:
            logger.warning(f"Could not extract text from PDF: {e}")
            return ""
        
    def _extract_document_metadata(self, mock_response, pdf: PdfReader) -> IngestDocument:
        """
        Extract metadata from PDF document.
        """
        doc = IngestDocument()
        doc.url = mock_response.url
        doc.id = hashlib.md5(doc.url.encode('utf-8')).hexdigest()
        
        # Extract title from PDF metadata
        try:
            if pdf.metadata and pdf.metadata.title:
                doc.title = pdf.metadata.title
            else:
                # Fallback to filename without extension
                doc.title = Path(mock_response.file_path).stem
        except Exception as e:
            logger.info(f'Error extracting title from PDF {mock_response.file_path}: {e}')
            doc.title = Path(mock_response.file_path).stem
            
        # Extract description from PDF metadata
        try:
            if pdf.metadata and pdf.metadata.subject:
                doc.description = pdf.metadata.subject
            else:
                doc.description = doc.title
        except:
            doc.description = doc.title
        
        # Extract keywords from PDF metadata
        try:
            if pdf.metadata and pdf.metadata.keywords:
                doc.keywords = [kw.strip() for kw in pdf.metadata.keywords.split(',')]
        except:
            pass
        
        # Extract creation date from PDF metadata
        try:
            if pdf.metadata and pdf.metadata.creation_date:
                doc.published_time = pdf.metadata.creation_date.isoformat() + "Z"
            else:
                # Fallback to file modification time
                file_stat = os.stat(mock_response.file_path)
                doc.published_time = datetime.fromtimestamp(file_stat.st_mtime).isoformat() + "Z"
        except:
            doc.published_time = datetime.utcnow().isoformat() + "Z"
            
        # For local PDFs, we'll try to extract locale from the file path or use default
        try:
            country, language = self.utils.get_country_and_language_from_url_and_locale(doc.url, None)
            doc.locale = self.utils.generate_locale(country, language, "en_US")  # Default to en_US
        except Exception as e:
            logger.warning(f"Could not extract locale from PDF path: {doc.url} -- {e}")
            doc.locale = "en_US"  # Default locale

        return doc


class MockPdfResponse:
    """
    Mock response object to maintain compatibility with existing PDF extractor interface.
    """
    
    def __init__(self, file_path: str, pdf_content: bytes, base_url: str = None):
        self.file_path = file_path
        self.body = pdf_content
        
        # Generate URL from file path
        if base_url:
            # Use provided base URL
            relative_path = os.path.relpath(file_path)
            self.url = urljoin(base_url, relative_path.replace(os.sep, '/'))
        else:
            # Convert file path to file:// URL
            path = Path(file_path).resolve()
            self.url = path.as_uri()
        
        # Mock status for compatibility
        self.status = 200
        
        # Mock request object for compatibility
        self.request = MockRequest()
    
    @property
    def text(self):
        """Mock text property for compatibility."""
        return f"PDF file: {self.file_path}"


class MockRequest:
    """Mock request object for compatibility."""
    
    def __init__(self):
        self.headers = MockHeaders()


class MockHeaders:
    """Mock headers object for compatibility."""
    
    def get(self, key, default=None):
        # For local files, there's no referrer
        if key == 'Referer':
            return default
        return default
