
from typing import List, Dict, Set, Tuple
from datetime import datetime


class IngestDocument:

    def __init__(self):
        self.id: str = None
        self.url: str = None
        self.title: str = None
        self.body: str = None
        self.hash: str = None
        self.locale: str = None
        self.description: str = None
        self.keywords: List[str] = None
        self.published_time: str = None
        self.ingest_time: str = None
        self.index: str = None
        self.pipeline: str = None
        self.latency: float = None
        self.ingest_duration: float = None
        self.depth: str = None
        self.referrer: str = None
        self.ingest_action: str = None
        self.size: str = None
        self.file_type: str = None

    def to_json(self):
        jobj: any = {"url": self.url, "body": self.body}
        jobj["doc_id"] = self.id
        if self.hash is not None:
            jobj["hash"] = self.hash
        if self.title is not None:
            jobj["title"] = self.title
        if self.locale is not None:
            jobj["locale"] = self.locale
        if self.description is not None:
            jobj["description"] = self.description
        if self.keywords is not None:
            jobj["keywords"] = self.keywords
        if self.published_time is not None:
            jobj["published_time"] = self.published_time
        if self.ingest_time is not None:
            jobj["ingest_time"] = self.ingest_time
        if self.ingest_duration is not None:
            jobj["ingest_duration"] = self.ingest_duration
        if self.latency is not None:
            jobj["latency"] = self.latency
        if self.depth is not None:
            jobj["depth"] = self.depth
        if self.referrer is not None:
            jobj["referrer"] = self.referrer
        if self.description is not None and self.locale is not None:
            jobj[f"suggest_{self.locale}"] = self.body
        if self.size is not None:
            jobj["size"] = self.size
        if self.file_type is not None:
            jobj["file_type"] = self.file_type
        return jobj


class SearchResultPassage:

    def __init__(self):
        self.id: str = None
        self.index: str = None
        self.score: float = None
        self.text: str = None

    def __str__(self):
        return '[{}%] {}'.format(int(self.score*100), self.text.replace("\n", ""))


class SearchResultDocument:

    def __init__(self):
        self.id: str = None
        self.index: str = None
        self.title: str = None
        self.score: float = None
        self.text: str = None
        self.url: str = None
        self.site: str = None
        self.locale: str = None
        self.description: str = None
        self.keywords: List[str] = []
        self.passages: List[SearchResultPassage] = []

    def __str__(self):
        out: str = "\n"
        if self.score is not None:
            out += f"[{self.score:.{1}f}]"
        if self.title is not None:
            out += f" {self.title}"
        if self.site is not None:
            out += f" ({self.site})"
        if self.id is not None:
            out += f"\n[{self.id}]"
        # if self.text is not None:
        #     out+= f" {self.text}"
        if self.url is not None:
            out += f"\n({self.url})"
        if self.description is not None:
            out += f"\n| {self.description}"
        # if self.text is not None:
        #     out+= f"\n{self.text}"
        if len(self.passages) > 0:
            out += '\n' + '\n'.join([f'\t{p}' for p in self.passages])
        return out

    def from_json(jobj: any):
        obj = SearchResultDocument()
        obj.title = jobj.get('title')
        obj.score = jobj.get('score', obj.score)
        obj.text = jobj.get('text')
        obj.description = jobj.get('description')
        obj.site = jobj.get('site')
        obj.url = jobj.get('url')
        return obj

    def to_json(self):
        jobj: any = {"title": self.title, "text": self.text,
                     "url": self.url, "site": self.site}
        if self.score is not None:
            jobj["score"] = self.score
        if self.site is not None:
            jobj["site"] = self.site
        if self.description is not None:
            jobj["description"] = self.description
        return jobj


class Search:

    def __init__(self):
        self.id: str = None
        self.query: float = None
        self.results: List[SearchResultDocument] = []

    def __str__(self):
        return f'[{self.id}] {self.query} ({len(self.results)}'

    def from_json(jobj: any):
        obj = Search()
        obj.id = jobj.get('id')
        obj.query = jobj.get('query')
        obj.results = [SearchResultDocument.from_json(
            r) for r in jobj.get('results')]
        return obj

    def to_json(self):
        jobj: any = {"id": self.id, "query": self.query, "results": [
            SearchResultDocument(r) for r in self.results]}


class IngestConfig:

    site: str = None
    index: str = None
    seeds: List[str] = []
    exclude: List[str] = []
    no_follow: List[str] = []
    must_have: List[str] = []

    def __init__(self, jobj: any):
        self.site = jobj.get('site')
        self.index = jobj.get('index')
        self.seeds = jobj.get('seeds')
        self.exclude = jobj.get('exclude', [])
        self.no_follow = jobj.get('no_follow', [])
        self.must_have = jobj.get('must_have', [])

    def __repr__(self):
        return f'{self.site} - {self.index}'


class MasterConfig:
    index: str = None
    pipeline: str = None

    def __init__(self, jobj: any):
        self.index = jobj.get('index')
        self.pipeline = jobj.get('pipeline')


class SpiderConfig:
    name: str = None
    index: str = None
    pipeline: str = None
    locale: str = None
    allowed_domains: List[str] = []
    seeds: List[str] = []
    depth: int = 1
    exclude: List[str] = []
    allow: List[str] = []
    no_follow: List[str] = []
    no_index: List[str] = []
    split: List[str] = []
    dynamic:bool = False

    def from_json(jobj: any):
        obj = SpiderConfig()
        obj.name = jobj.get('name')
        obj.index = jobj.get('index')
        obj.pipeline = jobj.get('pipeline')
        obj.locale = jobj.get('locale')
        obj.allowed_domains = jobj.get('allowed_domains',[])
        obj.seeds = jobj.get('seeds',[])
        obj.depth = jobj.get('depth',1)
        obj.exclude = jobj.get('exclude',[])
        obj.allow = jobj.get('allow',[])
        obj.no_follow = jobj.get('no-follow',[])
        obj.no_index = jobj.get('no-index',[])
        obj.split = jobj.get('split',[])
        obj.dynamic = jobj.get('dynamic', False)
        return obj
    
    def to_json(self):
        jobj: any = {}
        jobj["name"] = self.name
        jobj["index"] =self.index
        jobj["pipeline"] =self.pipeline
        jobj["locale"] =self.locale
        jobj["allowed_domains"] =self.allowed_domains
        jobj["seeds"] =self.seeds
        jobj["depth"] =self.depth
        jobj["exclude"] =self.exclude
        jobj["allow"] =self.allow
        jobj["no-follow"] =self.no_follow
        jobj["no-index"] =self.no_index
        jobj["split"] =self.split
        jobj["dynamic"] =self.dynamic
        return jobj
