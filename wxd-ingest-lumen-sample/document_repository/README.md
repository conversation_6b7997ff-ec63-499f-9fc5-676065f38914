# Local Files Directory

This directory is set up for you to add HTML and PDF files that will be processed by the Lumen ingestion system.

## Directory Structure

```
local_files/
├── README.md           # This file
├── documents/          # General documents (HTML and PDF)
├── html_files/         # Specifically for HTML files
└── pdf_files/          # Specifically for PDF files
```

## How to Use

### 1. Add Your Files
Simply copy your HTML and PDF files into any of these directories:
- **`documents/`** - For mixed file types
- **`html_files/`** - For HTML files (.html, .htm)
- **`pdf_files/`** - For PDF files (.pdf)

The system will automatically detect and process all supported files recursively.

### 2. Process the Files

#### Option A: Use the Command Line Tool
```bash
# Process all files in the local_files directory
python local_file_ingest_example.py \
    --directory local_files \
    --index lumen-local-docs \
    --recursive

# Process only HTML files
python local_file_ingest_example.py \
    --directory local_files/html_files \
    --index lumen-html-docs

# Process only PDF files  
python local_file_ingest_example.py \
    --directory local_files/pdf_files \
    --index lumen-pdf-docs
```

#### Option B: Use the Quick Processing Script
```bash
# Use the dedicated processing script (created below)
python process_local_files.py
```

#### Option C: Use Python Code
```python
from wxdingest.ingest.local_file_ingest_controller import LocalFileIngestController

controller = LocalFileIngestController()
stats = controller.ingest_directory(
    directory_path="local_files",
    index="lumen-local-docs",
    recursive=True
)
print(f"Processed {stats['processed']} files")
```

## Supported File Types

- **HTML**: `.html`, `.htm`
- **PDF**: `.pdf`

## Features

- **Automatic Discovery**: Files are automatically found in subdirectories
- **Metadata Extraction**: Titles, descriptions, and keywords are extracted
- **Locale Detection**: Language and locale are automatically detected
- **Error Handling**: Robust error handling with detailed logging
- **Statistics**: Comprehensive processing reports

## File Organization Tips

### For HTML Files
- Use descriptive filenames
- Include proper `<title>` tags
- Add meta description and keywords tags:
  ```html
  <meta name="description" content="Your description here">
  <meta name="keywords" content="keyword1, keyword2, keyword3">
  <meta name="locale" content="en_US">
  ```

### For PDF Files
- Use descriptive filenames
- Ensure PDFs have proper metadata (title, subject, keywords)
- Text-based PDFs work better than image-only PDFs

## Examples

### Sample HTML File Structure
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="Lumen service documentation">
    <meta name="keywords" content="lumen, documentation, guide">
    <meta name="locale" content="en_US">
    <title>Lumen Service Guide</title>
</head>
<body>
    <main>
        <h1>Lumen Service Guide</h1>
        <p>Your content here...</p>
    </main>
</body>
</html>
```

### Processing Results
After processing, you'll see output like:
```
=== PROCESSING SUMMARY ===
Total files found: 15
Successfully processed: 15
Errors: 0
HTML files: 10
PDF files: 5
New documents: 12
Updated documents: 3
Skipped documents: 0
```

## Next Steps

1. **Add your files** to any of the subdirectories
2. **Run the processing script** (see options above)
3. **Check the logs** for processing results
4. **Verify in WatsonX Discovery** that documents were indexed correctly

The system is ready to process your files as soon as you add them!
