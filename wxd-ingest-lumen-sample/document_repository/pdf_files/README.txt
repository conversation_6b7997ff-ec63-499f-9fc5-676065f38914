PDF Files Directory

Add your PDF files to this directory. The system will automatically process them.

Supported features:
- Text extraction from PDF content
- Metadata extraction (title, subject, keywords, creation date)
- Automatic locale detection
- Error handling for corrupted or protected PDFs

File requirements:
- Use .pdf extension
- Text-based PDFs work better than image-only PDFs
- Ensure PDFs are not password-protected
- Include proper metadata in the PDF for better indexing

The processing script will find and process all PDF files in this directory and its subdirectories.

To process your files, run:
python process_local_files.py

This text file will be ignored during processing as only .pdf files are processed from this directory.
