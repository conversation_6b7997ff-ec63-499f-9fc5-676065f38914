<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Getting started guide for Lumen local file processing">
    <meta name="keywords" content="lumen, getting started, guide, tutorial">
    <meta name="locale" content="en_US">
    <title>Getting Started with Lumen File Processing</title>
</head>
<body>
    <main>
        <h1>Getting Started with Lumen File Processing</h1>
        
        <p>Welcome to the Lumen local file processing system! This guide will help you get started with processing your HTML and PDF documents.</p>
        
        <h2>Quick Start</h2>
        <ol>
            <li><strong>Add your files</strong> to the local_files directory</li>
            <li><strong>Run the processing script:</strong> <code>python process_local_files.py</code></li>
            <li><strong>Check the results</strong> in WatsonX Discovery</li>
        </ol>
        
        <h2>Directory Structure</h2>
        <p>You can organize your files in the following structure:</p>
        <pre>
local_files/
├── documents/          # Mixed HTML and PDF files
├── html_files/         # HTML files only
└── pdf_files/          # PDF files only
        </pre>
        
        <h2>File Requirements</h2>
        
        <h3>HTML Files</h3>
        <ul>
            <li>Use .html or .htm extensions</li>
            <li>Include proper meta tags for better indexing</li>
            <li>Use semantic HTML structure</li>
            <li>Ensure content is within &lt;main&gt; tags when possible</li>
        </ul>
        
        <h3>PDF Files</h3>
        <ul>
            <li>Use .pdf extension</li>
            <li>Text-based PDFs work better than image-only PDFs</li>
            <li>Include proper metadata (title, subject, keywords) in the PDF</li>
            <li>Ensure the PDF is not password-protected</li>
        </ul>
        
        <h2>Processing Features</h2>
        <ul>
            <li><strong>Automatic Discovery:</strong> Files are found recursively in subdirectories</li>
            <li><strong>Metadata Extraction:</strong> Titles, descriptions, and keywords are extracted</li>
            <li><strong>Locale Detection:</strong> Language and locale are automatically detected</li>
            <li><strong>Error Handling:</strong> Robust error handling with detailed logging</li>
            <li><strong>Statistics:</strong> Comprehensive processing reports</li>
        </ul>
        
        <h2>Next Steps</h2>
        <p>After reading this guide:</p>
        <ol>
            <li>Delete these sample files</li>
            <li>Add your own HTML and PDF files</li>
            <li>Run <code>python process_local_files.py</code></li>
            <li>Check the processing results</li>
            <li>Verify your documents in WatsonX Discovery</li>
        </ol>
        
        <p><em>This is a sample file that can be deleted once you understand the system.</em></p>
    </main>
</body>
</html>
