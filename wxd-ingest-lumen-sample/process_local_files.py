#!/usr/bin/env python3
"""
Process Local Files Script for Lumen

This script is specifically configured to process HTML and PDF files
from the local_files directory. Simply add your files to the local_files
folder and run this script.

Usage:
    python process_local_files.py

The script will automatically:
- Find all HTML and PDF files in local_files/ directory
- Process them recursively through subdirectories
- Index them into WatsonX Discovery
- Provide detailed processing statistics
"""

import os
import sys
import logging
from pathlib import Path

# Add the wxdingest package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from wxdingest.ingest.local_file_ingest_controller import LocalFileIngestController

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
LOCAL_FILES_DIRECTORY = "local_files"
DEFAULT_INDEX = "lumen-local-docs"
DEFAULT_PIPELINE = "english"
DEFAULT_LOCALE = "en_US"
DEFAULT_BASE_URL = "https://lumen.local"
INSTANCE_NAME = "dev"

def check_directory():
    """Check if the local_files directory exists and has files."""
    if not os.path.exists(LOCAL_FILES_DIRECTORY):
        logger.error(f"Directory {LOCAL_FILES_DIRECTORY} does not exist!")
        logger.info("Please create the directory and add your HTML/PDF files.")
        return False
    
    if not os.path.isdir(LOCAL_FILES_DIRECTORY):
        logger.error(f"{LOCAL_FILES_DIRECTORY} is not a directory!")
        return False
    
    # Check for supported files
    supported_extensions = ['.html', '.htm', '.pdf']
    found_files = []
    
    for root, dirs, files in os.walk(LOCAL_FILES_DIRECTORY):
        for file in files:
            if any(file.lower().endswith(ext) for ext in supported_extensions):
                found_files.append(os.path.join(root, file))
    
    if not found_files:
        logger.warning(f"No HTML or PDF files found in {LOCAL_FILES_DIRECTORY}")
        logger.info("Please add some .html, .htm, or .pdf files to the directory.")
        logger.info("You can organize them in subdirectories like:")
        logger.info("  - local_files/documents/")
        logger.info("  - local_files/html_files/")
        logger.info("  - local_files/pdf_files/")
        return False
    
    logger.info(f"Found {len(found_files)} files to process:")
    for file_path in found_files[:10]:  # Show first 10 files
        logger.info(f"  - {file_path}")
    if len(found_files) > 10:
        logger.info(f"  ... and {len(found_files) - 10} more files")
    
    return True

def main():
    """Main processing function."""
    logger.info("=" * 60)
    logger.info("Lumen Local File Processing Script")
    logger.info("=" * 60)
    
    # Check if directory exists and has files
    if not check_directory():
        return 1
    
    try:
        # Initialize the controller
        logger.info(f"Initializing LocalFileIngestController for instance: {INSTANCE_NAME}")
        controller = LocalFileIngestController(instance_name=INSTANCE_NAME)
        
        # Process the directory
        logger.info(f"Starting processing of {LOCAL_FILES_DIRECTORY} directory...")
        logger.info(f"Target index: {DEFAULT_INDEX}")
        logger.info(f"Pipeline: {DEFAULT_PIPELINE}")
        logger.info(f"Default locale: {DEFAULT_LOCALE}")
        logger.info(f"Base URL: {DEFAULT_BASE_URL}")
        
        stats = controller.ingest_directory(
            directory_path=LOCAL_FILES_DIRECTORY,
            index=DEFAULT_INDEX,
            pipeline=DEFAULT_PIPELINE,
            locale_default=DEFAULT_LOCALE,
            base_url=DEFAULT_BASE_URL,
            recursive=True,
            force_refresh=False
        )
        
        # Print detailed results
        logger.info("=" * 60)
        logger.info("PROCESSING COMPLETED SUCCESSFULLY!")
        logger.info("=" * 60)
        logger.info(f"📁 Total files found: {stats['total_files']}")
        logger.info(f"✅ Successfully processed: {stats['processed']}")
        logger.info(f"❌ Errors: {stats['errors']}")
        logger.info(f"🌐 HTML files: {stats['html_files']}")
        logger.info(f"📄 PDF files: {stats['pdf_files']}")
        logger.info(f"🆕 New documents: {stats['new_documents']}")
        logger.info(f"🔄 Updated documents: {stats['updated_documents']}")
        logger.info(f"⏭️  Skipped documents: {stats['skipped_documents']}")
        
        if stats['errors'] > 0:
            logger.warning(f"⚠️  Processing completed with {stats['errors']} errors")
            logger.info("Check the logs above for error details")
            return 1
        else:
            logger.info("🎉 All files processed successfully!")
            
        # Provide next steps
        logger.info("=" * 60)
        logger.info("NEXT STEPS:")
        logger.info(f"1. Check WatsonX Discovery index '{DEFAULT_INDEX}' for your documents")
        logger.info("2. Add more files to local_files/ directory and run this script again")
        logger.info("3. Use the WatsonX Discovery search interface to find your content")
        logger.info("=" * 60)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Error during processing: {e}")
        logger.info("=" * 60)
        logger.info("TROUBLESHOOTING:")
        logger.info("1. Make sure WatsonX Discovery is properly configured in .env file")
        logger.info("2. Check that the instance name is correct")
        logger.info("3. Verify network connectivity to WatsonX Discovery")
        logger.info("4. Check file permissions on the local_files directory")
        logger.info("=" * 60)
        return 1

def show_help():
    """Show help information."""
    print("""
Lumen Local File Processing Script

This script processes HTML and PDF files from the local_files directory.

SETUP:
1. Add your HTML and PDF files to the local_files/ directory
2. Organize them in subdirectories if desired:
   - local_files/documents/
   - local_files/html_files/
   - local_files/pdf_files/

USAGE:
    python process_local_files.py

CONFIGURATION:
You can modify the configuration at the top of this script:
- LOCAL_FILES_DIRECTORY: Directory to scan for files
- DEFAULT_INDEX: WatsonX Discovery index name
- DEFAULT_PIPELINE: Processing pipeline
- DEFAULT_LOCALE: Default document locale
- DEFAULT_BASE_URL: Base URL for document URLs
- INSTANCE_NAME: WatsonX Discovery instance

SUPPORTED FILES:
- HTML files: .html, .htm
- PDF files: .pdf

The script will recursively process all subdirectories.
""")

if __name__ == '__main__':
    # Check for help flag
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
        sys.exit(0)
    
    # Run the main processing
    exit_code = main()
    sys.exit(exit_code)
