#!/usr/bin/env python3
"""
Lumen Local File Ingest Example

This script demonstrates how to use the new local file processing capabilities
to ingest HTML and PDF files from local directories into WatsonX Discovery.

Usage:
    python local_file_ingest_example.py --directory /path/to/files --index lumen-docs

Features:
    - Process HTML and PDF files from local directories
    - Recursive directory scanning
    - Maintain existing document lifecycle management
    - Support for custom base URLs and locales
    - Compatible with existing WatsonX Discovery infrastructure
"""

import argparse
import os
import sys
from pathlib import Path

# Add the wxdingest package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from wxdingest.ingest.local_file_ingest_controller import LocalFileIngestController
from wxdingest import config
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    parser = argparse.ArgumentParser(
        description='Ingest HTML and PDF files from local directories into WatsonX Discovery'
    )
    
    parser.add_argument(
        '--directory', '-d',
        required=True,
        help='Directory containing HTML and PDF files to ingest'
    )
    
    parser.add_argument(
        '--index', '-i',
        required=True,
        help='WatsonX Discovery index name'
    )
    
    parser.add_argument(
        '--pipeline', '-p',
        default='english',
        help='Processing pipeline (default: english)'
    )
    
    parser.add_argument(
        '--locale',
        default='en_US',
        help='Default locale for documents (default: en_US)'
    )
    
    parser.add_argument(
        '--base-url',
        help='Base URL for generating document URLs (optional)'
    )
    
    parser.add_argument(
        '--recursive', '-r',
        action='store_true',
        default=True,
        help='Process subdirectories recursively (default: True)'
    )
    
    parser.add_argument(
        '--force-refresh', '-f',
        action='store_true',
        help='Force update all documents regardless of changes'
    )
    
    parser.add_argument(
        '--instance',
        default='dev',
        help='WatsonX Discovery instance name (default: dev)'
    )
    
    parser.add_argument(
        '--single-file',
        help='Process a single file instead of a directory'
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.single_file:
        if not os.path.exists(args.single_file):
            logger.error(f"File does not exist: {args.single_file}")
            return 1
        if not os.path.isfile(args.single_file):
            logger.error(f"Path is not a file: {args.single_file}")
            return 1
    else:
        if not os.path.exists(args.directory):
            logger.error(f"Directory does not exist: {args.directory}")
            return 1
        if not os.path.isdir(args.directory):
            logger.error(f"Path is not a directory: {args.directory}")
            return 1
    
    # Initialize the controller
    try:
        controller = LocalFileIngestController(instance_name=args.instance)
        logger.info(f"Initialized LocalFileIngestController for instance: {args.instance}")
    except Exception as e:
        logger.error(f"Failed to initialize controller: {e}")
        return 1
    
    try:
        if args.single_file:
            # Process single file
            logger.info(f"Processing single file: {args.single_file}")
            result = controller.ingest_file(
                file_path=args.single_file,
                index=args.index,
                pipeline=args.pipeline,
                locale_default=args.locale,
                base_url=args.base_url,
                force_refresh=args.force_refresh
            )
            
            if result:
                logger.info(f"Successfully processed file: {result.url}")
                logger.info(f"Action: {result.ingest_action}, Duration: {result.ingest_duration:.2f}s")
            else:
                logger.error("Failed to process file")
                return 1
                
        else:
            # Process directory
            logger.info(f"Processing directory: {args.directory}")
            stats = controller.ingest_directory(
                directory_path=args.directory,
                index=args.index,
                pipeline=args.pipeline,
                locale_default=args.locale,
                base_url=args.base_url,
                recursive=args.recursive,
                force_refresh=args.force_refresh
            )
            
            # Print summary
            logger.info("=== PROCESSING SUMMARY ===")
            logger.info(f"Total files found: {stats['total_files']}")
            logger.info(f"Successfully processed: {stats['processed']}")
            logger.info(f"Errors: {stats['errors']}")
            logger.info(f"HTML files: {stats['html_files']}")
            logger.info(f"PDF files: {stats['pdf_files']}")
            logger.info(f"New documents: {stats['new_documents']}")
            logger.info(f"Updated documents: {stats['updated_documents']}")
            logger.info(f"Skipped documents: {stats['skipped_documents']}")
            
            if stats['errors'] > 0:
                logger.warning(f"Processing completed with {stats['errors']} errors")
                return 1
    
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        return 1
    
    logger.info("Local file ingest completed successfully!")
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
