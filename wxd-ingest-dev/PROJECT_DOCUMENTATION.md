# WatsonX Discovery Ingest - Complete Project Documentation

## Overview

This is a **web crawling and document ingestion system** built for UPS that uses **Scrapy** to crawl websites and automatically ingest content into **WatsonX Discovery (wxD)** for search and analytics.

### Main Purpose
- **Crawls UPS websites** (ups.com, developers, healthcare, investors, etc.)
- **Extracts content** from HTML pages and PDF documents
- **Processes and enriches** the content (language detection, locale extraction, etc.)
- **Indexes documents** into WatsonX Discovery with proper metadata
- **Manages document lifecycle** (handles updates, deletions, and stale content)

## System Architecture

```mermaid
graph TB
    subgraph "Configuration Layer"
        SC[Spider Configs<br/>JSON files]
        WXD_CONFIG[WatsonX Discovery<br/>spider-configs index]
        ENV[Environment Variables<br/>.env file]
    end
    
    subgraph "Control Layer"
        IC[IngestController<br/>Main orchestrator]
        SCM[SpiderConfigManager<br/>Config loader]
        IM[InstancesManager<br/>WxD connections]
    end
    
    subgraph "Crawling Layer"
        TS[TemplateSpider<br/>Scrapy spider]
        MW[Middlewares<br/>Request/Response handling]
        SPIDERS[Specific Spiders<br/>ups-com, developers, etc.]
    end
    
    subgraph "Processing Layer"
        PP[PageProcessor<br/>Document orchestrator]
        HE[HtmlExtractor<br/>HTML content extraction]
        PE[PdfExtractor<br/>PDF content extraction]
        IU[IngestUtils<br/>Locale & language utils]
    end
    
    subgraph "Storage Layer"
        ES[Elasticsearch<br/>WatsonX Discovery]
        IDX[Document Indices<br/>ups-com, developers, etc.]
        PIPE[ML Pipelines<br/>Text processing]
    end
    
    subgraph "Management Layer"
        SCRAPYD[Scrapyd Server<br/>Spider management]
        SCRAPYDWEB[ScrapydWeb UI<br/>Web interface]
        TIMER[Timer Tasks<br/>Scheduled crawls]
    end
```

## Document Processing Workflow

```mermaid
flowchart TD
    START([Crawler Starts]) --> LOAD_CONFIG[Load Spider Config<br/>from wxD or local file]
    LOAD_CONFIG --> INIT_SPIDER[Initialize TemplateSpider<br/>with config parameters]
    INIT_SPIDER --> SEED_URLS[Process Seed URLs<br/>from config]
    
    SEED_URLS --> DISCOVER_PAGE[Discover New Page/URL]
    DISCOVER_PAGE --> CHECK_RULES{Apply Crawling Rules<br/>allow/exclude/depth}
    
    CHECK_RULES -->|Allowed| FETCH_PAGE[Fetch Page Content<br/>via Scrapy]
    CHECK_RULES -->|Blocked| DISCOVER_PAGE
    
    FETCH_PAGE --> MIDDLEWARE[Process through<br/>Middlewares]
    MIDDLEWARE --> DETERMINE_TYPE{Determine<br/>Content Type}
    
    DETERMINE_TYPE -->|HTML| HTML_EXTRACT[HtmlExtractor<br/>Extract metadata & body]
    DETERMINE_TYPE -->|PDF| PDF_EXTRACT[PdfExtractor<br/>Extract metadata & body]
    DETERMINE_TYPE -->|Other| SKIP[Skip Processing]
    
    HTML_EXTRACT --> CREATE_DOC[Create IngestDocument<br/>with extracted data]
    PDF_EXTRACT --> CREATE_DOC
    
    CREATE_DOC --> ENRICH_DOC[Enrich Document<br/>- Extract locale from URL<br/>- Generate description<br/>- Calculate hash<br/>- Set timestamps]
    
    ENRICH_DOC --> CHECK_EXISTS{Document exists<br/>in wxD index?}
    
    CHECK_EXISTS -->|No| INDEX_NEW[Index as New Document<br/>Full ML processing]
    CHECK_EXISTS -->|Yes| CHECK_HASH{Hash changed?<br/>Content modified?}
    
    CHECK_HASH -->|Changed| UPDATE_DOC[Update Document<br/>Full ML processing]
    CHECK_HASH -->|Same| UPDATE_TIME[Update ingest_time only<br/>Mark as current]
    
    INDEX_NEW --> CREATE_ITEM[Create UpsIngestItem<br/>for monitoring]
    UPDATE_DOC --> CREATE_ITEM
    UPDATE_TIME --> CREATE_ITEM
    
    CREATE_ITEM --> LOG_ITEM[Log Processing Results]
    LOG_ITEM --> MORE_LINKS{More links<br/>to follow?}
    
    MORE_LINKS -->|Yes| DISCOVER_PAGE
    MORE_LINKS -->|No| PURGE_CHECK[Check for Stale Documents<br/>Not crawled in this run]
    
    PURGE_CHECK --> PURGE_OLD{Purge enabled<br/>& documents older<br/>than stale_days?}
    
    PURGE_OLD -->|Yes| DELETE_STALE[Delete Stale Documents<br/>from wxD index]
    PURGE_OLD -->|No| LOG_STALE[Log Stale Documents<br/>but don't delete]
    
    DELETE_STALE --> END([Crawl Complete])
    LOG_STALE --> END
    SKIP --> MORE_LINKS
```

## Configuration and Deployment Architecture

```mermaid
graph TB
    subgraph "Development Environment"
        DEV_CONFIG[Local Spider Configs<br/>JSON files]
        DEV_ENV[Local .env file<br/>Development settings]
        DEV_CODE[wxdingest Code<br/>Python modules]
    end
    
    subgraph "Configuration Management"
        CONFIG_UPLOAD[Upload Configs<br/>SpiderConfigManager]
        WXD_CONFIGS[WatsonX Discovery<br/>spider-configs index]
        CONFIG_PARAMS[Configuration Parameters<br/>- name, index, pipeline<br/>- allowed_domains, seeds<br/>- depth, exclude, allow<br/>- no-follow, no-index]
    end
    
    subgraph "Container Environment"
        DOCKERFILE[Dockerfile<br/>Multi-service container]
        SCRAPY_SERVICE[Scrapy Framework<br/>Web crawling engine]
        SCRAPYD_SERVICE[Scrapyd Server<br/>Spider management daemon]
        SCRAPYDWEB_SERVICE[ScrapydWeb UI<br/>Web management interface]
        SUPERVISOR[Supervisord<br/>Process management]
    end
    
    subgraph "Runtime Environment"
        SPIDER_INSTANCES[Spider Instances<br/>ups-com, developers,<br/>healthcare, investors, etc.]
        TIMER_TASKS[Scheduled Timer Tasks<br/>Cron-like scheduling]
        LOG_MONITORING[Log Monitoring<br/>ScrapydWeb dashboard]
    end
    
    subgraph "Target Systems"
        WXD_INSTANCES[WatsonX Discovery Instances<br/>dev, pt, staging, prod-east, prod-central]
        INDICES[Document Indices<br/>Separate index per site/locale]
        ML_PIPELINES[ML Processing Pipelines<br/>Text analysis & enrichment]
    end
```

## Key Components

### 1. Project Structure
```
wxd-ingest-dev/
├── wxdingest/                    # Main Python package
│   ├── ingest/                   # Core crawling and processing
│   │   ├── template.py           # Main spider template
│   │   ├── page_processor.py     # Document processing orchestrator
│   │   ├── html_extractor.py     # HTML content extraction
│   │   ├── pdf_extractor.py      # PDF content extraction
│   │   ├── ingest_controller.py  # Main controller
│   │   └── deploy/ups_ingest/    # Deployment-specific spiders
│   ├── setup/                    # Configuration management
│   │   ├── spider_config_manager.py
│   │   └── instances_manager.py
│   ├── elasticsearch_interface.py # wxD/Elasticsearch interface
│   ├── model.py                  # Data models
│   └── config.py                 # Configuration handling
├── config/                       # Configuration files
│   ├── countries.txt
│   ├── languages.txt
│   └── indices/
├── container/                    # Docker containerization
└── requirements.txt
```

### 2. Spider Configuration Model
```python
class SpiderConfig:
    name: str = None           # Spider identifier
    index: str = None          # Target wxD index
    pipeline: str = None       # ML processing pipeline
    locale: str = None         # Default locale
    allowed_domains: List[str] = []  # Domains to crawl
    seeds: List[str] = []      # Starting URLs
    depth: int = 1             # Crawl depth limit
    exclude: List[str] = []    # URLs to exclude (regex)
    allow: List[str] = []      # URLs to allow (regex)
    no_follow: List[str] = []  # Don't follow links from these
    no_index: List[str] = []   # Don't index these URLs
    split: List[str] = []      # URL splitting rules
    dynamic: bool = False      # Dynamic content handling
```

### 3. Document Model
```python
class IngestDocument:
    id: str = None             # Document ID (MD5 of URL)
    url: str = None            # Source URL
    title: str = None          # Document title
    body: str = None           # Extracted text content
    hash: str = None           # Content hash for change detection
    locale: str = None         # Document locale (e.g., en_US)
    description: str = None    # Document description
    keywords: List[str] = None # Keywords/tags
    published_time: str = None # Publication timestamp
    ingest_time: str = None    # Last ingestion timestamp
    index: str = None          # Target index
    pipeline: str = None       # Processing pipeline
    latency: float = None      # Processing latency
    ingest_duration: float = None # Total processing time
    depth: str = None          # Crawl depth
    referrer: str = None       # Referring page
    ingest_action: str = None  # Action taken (new/update/skip)
    size: str = None           # Document size
    file_type: str = None      # Content type (HTML/PDF)
```

## Core Processing Components

### 1. IngestController - Main Orchestrator
**Location**: `wxdingest/ingest/ingest_controller.py`

The main entry point that coordinates the entire crawling process:

```python
class IngestController:
    def ingest(self, config_name: str):
        # Load scrapy settings
        settings: Settings = self._load_scrapy_settings()
        # Start crawling
        self._start_crawl(settings, config_name)

    def _start_crawl(self, settings: Settings, config_name:str) -> None:
        c = CrawlerProcess(settings)
        c.crawl(TemplateSpider, config_name=config_name, purge="false", force_refresh="false")
        c.start()
```

### 2. TemplateSpider - Core Crawler
**Location**: `wxdingest/ingest/template.py`

The main Scrapy spider that handles URL discovery and page processing:

```python
class TemplateSpider(CrawlSpider):
    def parse_item(self, response):
        # Determine content type and route to appropriate processor
        if response.url.endswith("pdf"):
            doc = PdfExtractor(instance_name=self.instance_name).extract(response, self.index)
        else:
            doc = HtmlExtractor().extract(response)

        # Process and index the document
        doc = self.processor.process_page(response, self.index, self.pipeline,
                                        locale_default=self.locale,
                                        force_refresh=self.force_doc_refresh)
```

### 3. PageProcessor - Document Processing Orchestrator
**Location**: `wxdingest/ingest/page_processor.py`

Coordinates the entire document processing pipeline:

```python
def process_page(self, response, index: str, pipeline: str, locale_default: str,
                *, log=None, force_refresh:bool=False):
    start_time = time()

    # Route to appropriate extractor
    if response.url.endswith("pdf"):
        if self.PDF_INDEX:
            index += "-pdf"
        doc = PdfExtractor(instance_name=self.instance_name).extract(response,index)
    else:
        doc = HtmlExtractor().extract(response)

    # Enrich document with metadata
    doc.hash = self._hash_document(doc)
    doc.size = self._get_text_size(doc.body)

    # Send to wxD for indexing
    response = self._ingest_document(doc, index, pipeline, force_refresh=force_refresh)

    doc.ingest_duration = time() - start_time
    return doc
```

### 4. HtmlExtractor - HTML Content Processing
**Location**: `wxdingest/ingest/html_extractor.py`

Extracts content and metadata from HTML pages:

```python
class HtmlExtractor:
    def extract(self, response) -> IngestDocument:
        doc: IngestDocument = self._extract_document_metadata(response)
        doc.body = self._extract_body(response)
        doc.file_type = "HTML"
        return doc

    def _extract_body(response) -> str:
        # Try to extract main content first
        html: str = response.xpath('//main').get()
        if html is None:
            html = response.body

        # Convert HTML to clean text using html2text
        h = html2text.HTML2Text()
        h.ignore_links = True
        h.skip_internal_links = True
        h.inline_links = False
        h.ignore_images = True
        return h.handle(html)
```

### 5. PdfExtractor - PDF Content Processing
**Location**: `wxdingest/ingest/pdf_extractor.py`

Extracts content and metadata from PDF documents:

```python
class PdfExtractor:
    def extract(self, response, index:str) -> IngestDocument:
        try:
            pdf:PdfReader = PdfReader(BytesIO(response.body))
            # Extract metadata
            doc:IngestDocument = self._extract_document_metadata(response, pdf, index)
            # Extract body text
            doc.body = self._extract_body(pdf)
            # Generate description using AI
            doc.description = self.pdf_description_generator.describe_single_pdf(doc)
            doc.file_type = "PDF"
            return doc
        except Exception as e:
            logger.warning(f"Could not parse PDF {response.url}: {e}")

    def _extract_body(self, pdf:PdfReader) -> str:
        return "\n".join([p.extract_text(extraction_mode="plain",
                                       layout_mode_space_vertically=False)
                         for p in pdf.pages])
```

### 6. SpiderConfigManager - Configuration Management
**Location**: `wxdingest/setup/spider_config_manager.py`

Manages loading and storing spider configurations:

```python
class SpiderConfigManager:
    def load_spider_config(self, config_name:str) -> SpiderConfig:
        if self.load_from_local:
            return self._load_spider_config_from_local(config_name)
        else:
            return self._download_spider_config_from_wxd(config_name)

    def upload_spider_config_from_file(self, config_path:str) -> None:
        with open(config_path, 'r') as fin:
            config = SpiderConfig.from_json(json.load(fin))
            self.elastic.ingest_document(config.to_json(), id=config.name,
                                       index=self.index_name)
```

### 7. ElasticsearchInterface - WatsonX Discovery Integration
**Location**: `wxdingest/elasticsearch_interface.py`

Handles all interactions with WatsonX Discovery/Elasticsearch:

```python
class ElasticsearchInterface:
    def ingest_document(self, document: Mapping[str, Any], id: str = None,
                       index: str = None, pipeline: str = None, **kwargs):
        res = self.client.index(index=index, document=document,
                               id=id, pipeline=pipeline, **kwargs)
        _ = self.client.indices.refresh(index=index)
        return res

    def query(self, index:str, query_body: Dict, **kwargs ):
        res = self.client.search(index=index, **query_body).body
        return res
```

## Configuration Parameters

### Environment Variables
- `ELASTICSEARCH_URL`: WatsonX Discovery endpoint
- `ELASTICSEARCH_USER_NAME`: Authentication username
- `ELASTICSEARCH_PASSWORD`: Authentication password
- `LOCAL_CONFIGS`: Load configs from local files (true/false)
- `PURGE`: Enable deletion of stale documents (true/false)
- `STALE_DAYS`: Days before documents are considered stale (default: 10)
- `FORCE_REFRESH`: Force update all documents regardless of hash (true/false)
- `PDF_INDEX`: Store PDFs in separate index with "-pdf" suffix (true/false)
- `WXD_TIMEOUT`: Timeout for wxD operations in seconds (default: 60)
- `LOG_LEVEL`: Logging level (DEBUG/INFO/WARNING/ERROR)
- `DOWNLOAD_DELAY`: Delay between requests in seconds (default: 0.1)
- `CONCURRENT_REQUESTS`: Number of concurrent requests (default: 50)

### Spider Configuration Fields
- `name`: Unique identifier for the spider
- `index`: Target WatsonX Discovery index name
- `pipeline`: ML processing pipeline (usually "english")
- `locale`: Default locale if not detected (e.g., "en_US")
- `allowed_domains`: List of domains the spider can crawl
- `seeds`: Starting URLs for the crawl
- `depth`: Maximum crawl depth (0 = seeds only, 1 = seeds + 1 level)
- `exclude`: Regex patterns for URLs to exclude from crawling
- `allow`: Regex patterns for URLs to specifically allow
- `no_follow`: URLs to index but not follow links from
- `no_index`: URLs to follow links from but not index
- `split`: URL patterns to split (remove parameters)
- `dynamic`: Enable dynamic content handling (JavaScript rendering)

## Deployment Architecture

### Container Stack
The system runs as a multi-service Docker container:

1. **Scrapy Framework**: Core web crawling engine
2. **Scrapyd Server**: Daemon for managing multiple spider instances
3. **ScrapydWeb UI**: Web interface for monitoring and control (port 5000)
4. **Supervisord**: Process management and monitoring
5. **wxdingest**: Custom spider implementations and processing logic

### Available Spider Instances
Based on the codebase, the following spiders are configured:

- `ups-com`: Main UPS website crawler
- `developers`: UPS Developer portal
- `healthcare`: UPS Healthcare solutions
- `investors`: UPS Investor relations
- `supply-chain-*`: Supply chain related content
- `test`: Testing and development spider

### WatsonX Discovery Instances
The system supports multiple deployment environments:

- `dev`: Development environment
- `pt`: Performance testing
- `staging`: Staging environment
- `prod-east`: Production East region
- `prod-central`: Production Central region

## Document Lifecycle Management

### Change Detection
The system uses content hashing to detect document changes:

1. **New Document**: Full indexing with ML processing
2. **Modified Document**: Hash comparison → full reprocessing if changed
3. **Unchanged Document**: Only update `ingest_time` timestamp
4. **Stale Document**: Remove if not crawled within `stale_days`

### Purge Process
After each crawl, the system can automatically clean up stale content:

- Documents not encountered in the current crawl are marked as stale
- Documents older than `STALE_DAYS` (default: 10) are deleted
- Conservative approach: only removes content definitively not found
- Can be disabled by setting `PURGE=false`

## Usage Examples

### Local Development Setup
```bash
# Setup virtual environment
python -m venv env
source env/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp template.env .env
# Edit .env with your WatsonX Discovery credentials

# Run a spider locally
python wxdingest/ingest/ingest_controller.py
```

### Running Specific Spiders
```python
# In ingest_controller.py, modify the main section:
if __name__ == '__main__':
    IngestController().ingest('ups-com')  # Run UPS.com spider
    # or
    IngestController().ingest('developers')  # Run developers spider
```

### Container Deployment
```bash
# Build container
sh container/build.sh <BUILD_PATH> <SCRAPYDWEB_SAML>

# Run container
docker run --env-file .env -v /path/to/scrapyd:/scrapyd -it -p 5000:5000 wxdingest
```

### Configuration Management
```bash
# Upload new spider config to WatsonX Discovery
upswebutils setup configs --instance dev --infile path/to/spider-config.json

# Backup all configurations
upswebutils setup backup --instance dev --outdir backup/path
```

## Monitoring and Troubleshooting

### ScrapydWeb Interface
Access the web interface at `http://localhost:5000` to:

- Monitor running spiders
- View crawl statistics and logs
- Schedule timer tasks
- Start/stop spider instances
- Review processing metrics

### Key Metrics
The system tracks various metrics for each document:

- **Latency**: Time to fetch the page
- **Ingest Duration**: Total processing time
- **Depth**: Crawl depth where document was found
- **Ingest Action**: Action taken (new/update/skip/error)
- **Document Size**: Character count of extracted text

### Log Monitoring
Key log messages to monitor:

- Spider startup and configuration loading
- Document processing results
- WatsonX Discovery indexing responses
- Error handling and recovery
- Purge process results

### Common Issues and Solutions

1. **Configuration Not Found**
   - Ensure spider config exists in WatsonX Discovery `spider-configs` index
   - Check `LOCAL_CONFIGS` environment variable setting

2. **Connection Timeouts**
   - Adjust `WXD_TIMEOUT` environment variable
   - Check WatsonX Discovery instance availability
   - Verify network connectivity and credentials

3. **Memory Issues with Large Documents**
   - Monitor PDF processing for large files
   - Consider splitting large crawls into smaller batches
   - Adjust container memory limits

4. **Stale Document Cleanup**
   - Review `STALE_DAYS` setting
   - Monitor purge process logs
   - Use `PURGE=false` for testing to prevent accidental deletions

## File Structure Reference

```
wxd-ingest-dev/
├── PROJECT_DOCUMENTATION.md     # This documentation file
├── README.md                    # Basic setup instructions
├── requirements.txt             # Python dependencies
├── template.env                 # Environment template
├── setup.py                     # Package setup
├── config/                      # Configuration files
│   ├── countries.txt           # Valid country codes
│   ├── languages.txt           # Valid language codes
│   ├── locales.txt             # Locale mappings
│   ├── pdf_description_prompt.txt  # AI prompt for PDF descriptions
│   ├── pdf_langid_prompt.txt   # AI prompt for language detection
│   └── indices/                # Index configuration templates
├── container/                   # Docker containerization
│   ├── Dockerfile              # Multi-service container
│   ├── build.sh               # Container build script
│   ├── container.env          # Container environment template
│   ├── supervisord.conf       # Process management config
│   └── scrapydweb_settings_v10.py  # Web UI configuration
├── data/images/                # Documentation images
└── wxdingest/                  # Main Python package
    ├── __init__.py
    ├── __version__.py          # Version information
    ├── config.py               # Configuration management
    ├── model.py                # Data models and classes
    ├── elasticsearch_interface.py  # WatsonX Discovery interface
    ├── ingest/                 # Core crawling and processing
    │   ├── ingest_controller.py    # Main orchestrator
    │   ├── template.py             # Base spider template
    │   ├── page_processor.py       # Document processing
    │   ├── html_extractor.py       # HTML content extraction
    │   ├── pdf_extractor.py        # PDF content extraction
    │   ├── pdf_description_generator.py  # AI-powered PDF descriptions
    │   ├── ingest_utils.py         # Utility functions
    │   └── deploy/ups_ingest/      # Deployment-specific code
    │       ├── items.py            # Scrapy item definitions
    │       ├── middlewares.py      # Request/response middleware
    │       ├── settings.py         # Scrapy settings
    │       └── spiders/            # Individual spider implementations
    ├── setup/                  # Configuration and setup management
    │   ├── setup_controller.py    # Setup orchestration
    │   ├── spider_config_manager.py  # Config management
    │   └── instances_manager.py    # WxD instance management
    └── utils/                  # Utility modules
        └── applog.py           # Logging utilities
```

This documentation provides a complete reference for understanding, deploying, and maintaining the WatsonX Discovery Ingest system.
