# watsonx Discovery Ingest

This repository contains scripts for ingesting web pages into WatsonX Discovery (wxD). The ingest process is initiated by a crawler that is implemented as a [`scrapy`](https://www.scrapy.org) spider. The `TemplateSpider` class implement a `scrapy` spider and can be controlled via a spider configuration JSON file. One such spider config file exists for each of the UPS sites. The spider config files reside on watsonx Discovery in the `spider_configs` index. When starting the crawler, provide the name of the configuration. The crawler will then pull the respective configuration file from wxD and crawl pages according to that configuration. Whenever a page is encountered, the `PageProcessor` class is responsible for orchestrating the ingest. A separate `HtmlExtractor` and `PdfExtractor` class is implemented to handle exracting metadata and body text from the respective file types. Once the information is extracted, the document is sent to a specific pipeline and index in watsonx Discovery.

# Local setup

<details>
<summary>Details</summary>

To start working with this service, make sure to have Python `3.11` or higher installed. Then, clone the repository and navigate into the root folder. Now, set up and activate the virtual environment:

```bash
python -m venv env
source env/bin/activate
```

Next, install the dependencies:

```bash
pip install -r requirements.txt
```

Finally, create the `.env` file with the settings and credentials. Copy the template env file:

```bash
cp template.env .env
```

Open up the `.env` file and set Elasticsearch URL, user name, and password.

You are now ready to rock an roll.

</details>


# Container build process

<details>
<summary>Details</summary>

The container configuration provided in the `container` folder builds a container that runs crawler on a server and allows controlling it via a user interface. The following is makes up the stack of the container:

- `scrapy`: the library that performs the scraping of a site.
- `wxdingest`: implements specificy scrapy crawlers and ingesting crawled documents into watsonx Discovery
- `scrapyd`: server that can run multiple crawlers simultaneously.
- `scrapydweb`: user interface to manage crawlers running on the `scrapyd` server.

The `Dockerfile` builds a container that runs all the above components on one container image and exposes the UI via port `5000`. Note that this build process uses a modified version of `scrapydweb`. For the purposes of enterprise level access control, `scrapydweb` was modified to add support for SAML based authentication.

## Creating a build

Before building a container ensure that
- the `wxdingest` project (this project) is cloned
- the SAML version of `scrapydweb` is cloned.

Once these two projects are available, run the following command

```
sh container/build.sh <BUILD_PATH> <SCRAPYDWEB_SAML>
```

`BUILD_PATH` is a directory to which files will be copied and that serves as the docker context. `SCRAPYDWEB_SAML` is the path to the `scrapydweb` project that was customized to support SAML.

The build script will copy all necessary artifacts from `wxdingest` and `scrapydweb` to a target build directory. It will then build the container.

## Running the container

To run the container, first prepare a `.env` file that holds the required environment variables. A template is provided in `container/container.env`. Make sure to replate the `<REQUIRED>` tags with actual values.

Next create a directory where `scrapyd` can store logs and job information. That directory will be populated when the service starts up.

Then, run the following command:

```
docker run --env-file .env -v /path/to/scrapyd:/scrapyd -it -p 5000:5000 wxdingest
```

Replace `.env` with the path to your `.env` file. But if you are running this from the root folder of wxd-ingest, you already have a `.env` file. Just make sure it has all the parameters that are included in `container/container.env`

</details>

# Handling modified and deleted pages

<details>
<summary>Details</summary>

The crawler purges pages that appear stale after a crawl has been completed. That approach handles the following situations:
- **New page**: new pages are created in the index
- **Modified page**: the crawler compares the hash of the document in the index with the hash of the crawled page. If the hashes are different, the page is indexed, overwriting the existing document in the index. Currently, the hash is based on body text, title, description and published_date.
- **Removed page**: a page that is not crawled, can be removed if so configured. 

The diagram below illustrates the ingest process starting with the crawler discovering a page. Once a page is identified and its content has been loaded, the ingest process starts.

![Ingest Process](data/images/ingest_process.svg)

In the nominal case, the index is queried for the document by document ID. That is necessary to check on the hash of the page and make the decision whether the page has been modified. Based on that check, the page is either sent to be indexed or to update the ingest date. More specifically, if the page is new or modified, the entire page is indexed and ML is applied to extract text vectors and semantic expansions. That is the most resource intensive scenario. If the page already exists and the page has not been modified, only the `ingest_time` attribute of the document is updated. That makes sure the page is marked as ingested while avoiding the overhead of applying the ML models. Hence, that action is faster and less resource intensive.

Occasionally, the crawler may encounter a timeout trying to fetch a page. In that case, the `ingest_time` is updated to make sure it won't be purged. Of course, the page may not exist anymore. But this strategy tries to take a conservative approach and assumes the page is not stale if no contradicting information can be found.

### Controlling the purge process

After the crawler completed crawling web pages, the purge process is initiated. In that process, the pages whose `ingest_time` is "old" are deleted automatically after the ingest run. Note, all pages that were crawled will have a new `ingest_time`. Hence, only pages that have not been found in the last crawl are purged.

The following **parameters** control the purge process:

- `PURGE` (`purge`): if set to `true` pages that have been identified as "old" are deleted after the crawl is complete. If this parameter is set to `false` or is not defined, the log will output the pages that have been identified as stale but these pages will not be deleted. This is a more conservative approach to purging. It allows for human review before deleting pages. Default is `true`.
- `STALE_DAYS` (`stale_days`): if set to `0`, any pages whose `ingest_time` is older than the start of the crawl, will be deleted. However, that may be too aggressive. Setting this parameter to `10` will make sure that any pages that have been encountered during this or last week's ingest is not removed. That means that any removed pages will be deleted from the index 2 weeks after removal at the latest. Default is `10`

These parameters can be controlled from the pod by setting environment variables or using parameters in the crawler UI. The latter parameter names are shown in parenthesis above.

</details>


# Configuration variables

<details>
<summary>Details</summary>

The following lists the environment variables that can be used to control the crawling behavior. The `Environment` means that this is the name of the parameter as read from the environment (set in the config map for the container). The `Parameter` can be set in the crawler UI. After clicking on `Run Spider` and selecting the spider, click on `settings & arguments`. In the `additional` field, remove all parameters that have been pre-filled and provide your argument like so:

```
-d purge=true
-d stale_days=10
```

Make sure to start each line with `-d`.

## Parameters to control deleting stale pages

After every crawl, the code checks if any documents are older than a certain number of days and deltes those that are. These parameters control that behavior:

- **Activate purging**: A boolean flag that controls whether stale documents will be delted at the end of a crawl. If set to `false`, a message will be printed about how many documents are stale but the documents will not be deleted. If set to `true`, the documents will be deleted.
    - Environment: `PURGE`
    - Parameter: `purge`

- **Stale age**: A document is considered stale if the `ingest_date` is older than a certain number of days. The days can be configured with this parameter. The default is `10` days. That makes sure that documents that are "accidentally" not ingested during one ingestion run are not deleted the next week. It essentially leads to documents to be deleted that have not been encountered for two weeks.
    - Environment: `STALE_DAYS`
    - Parameter: `stale_days`

## Parameters for testing

- **Index name**: for testing, it is useful to be able to specify an alternative index to ingest into. When specifying this parameter, the documents will be ingested into this index rather than the index specified in the spider config.
    - Parameter: `index`

- **Update all documents**: during a typical ingest, a document is only overwritten if the hash code has changed. But for testing, we sometimes want to overwrite documents even if they haven't changed (e.g., after we added a new index parameter). This parameter will force all documents to be updated.
    - Environment: `FORCE_REFRESH`
    - Parameter: `force_refresh`

## Other parameters:

- **wxD timeout**: Timeout in seconds for indexing documents in watsonx Discovery. Default: `60`
    - Environment: `WXD_TIMEOUT`
    - Parameter: `wxd_timeout`

- **Split PDF index**: boolean flag that instructs the crawler to stored PDF documents into a separate index with a `pdf` suffix. Default: `true`
    - Environment: `PDF_INDEX`

- **Scrapy Environment**: environment variables that control scrapy behavior and are used to update the `settings.py`:
    - `LOG_LEVEL`: logging level (Default: `INFO`)
    - `DOWNLOAD_DELAY`: the delay between requests, in seconds. Default: `0.1`
    - `CONCURRENT_REQUESTS` = number of concurrent requests. Default: `50`
    - `CONCURRENT_REQUESTS_PER_DOMAIN` = number of concurrent requests per domain. Default: `50`
    - `CONCURRENT_REQUESTS_PER_IP` = number of concurrent requests per IP. Default: `50`
    - `AUTOTHROTTLE_ENABLED`: whether to enable the auto throttling feature. Default: `false`

</details>

# The spider config format

<details>
<summary>Details</summary>

Spider config files control the behavior of a crawler by defining what URLs to crawl, how deep to crawl, what index to push documents to, etc. The following describes the fields:

- `name`: The name of the crawler, which is how `wxdingest` finds the spider config file.
- `index`: The index to push documents to. When the crawl starts and the index doesn't exist, a new index will be created.
- `pipeline`: The pipeline to use for ingest. This must be set to `english`.
- `locale`: Allows to specify a fallback `locale` if one can't be extracted during the crawl. Should be set to `null` for most crawlers.
- `allowed_domains`: [scrapy documentation](https://docs.scrapy.org/en/latest/topics/spiders.html#scrapy.Spider.allowed_domains)
- `seeds`: corresponds to [start_urls](https://docs.scrapy.org/en/latest/topics/spiders.html#scrapy.Spider.allowed_domains) in `scrapy`
- `depth`: corresponds to [DEPTH_LIMIT](https://docs.scrapy.org/en/latest/topics/settings.html#depth-limit) in `scrapy`. However, unlike in `scrapy`, a depth of `0` means that only the seed URLs will be followed.
- `exclude`: list of regular expressions specifying what URLs not to include. Note, that is in addition to `allowed_domains`. That means if something is an `allowed_domain` but should not be ingested, it can be added to this list.
- `allow`: corresponds to the [allow](https://docs.scrapy.org/en/latest/topics/settings.html#depth-limit) attribute in `scrapy`.
- `no-follow`: The URLs that match any of the regular expressions in this list will be ingested. However, no links will be followed from any of the pages.
- `no-index`: The URLs that match any of the regular expressions in this list will NOT be ingested. But links from those pages will be followed.
- `split`: If a URL matches a regular expression in this list, it will be split and only the first element will be retained. This is useful when there are URL parameters that can be ignored.

All these rules are implemented in the `template.py` module in `wxdingest`


</details>

# Adding a new crawler

<details>
<summary>Details</summary>

The wxd-ingest project is set up to be flexible in accomodating new crawler. The following describes the steps to add a new crawler such that it can be executed from the crawler UI.


## (1) Create spider config

All crawlers are controlled by a JSON config file that specifies the rules, the crawler follows. Such a file must be created for the new crawler. Then, that file must be deployed to all watsonx Discovery instances from where crawlers access the configs.

To create a spider config, copy an existing spider config and modify it to suit your needs. The spider configs reside on every wxD instance in the `spider-configs` directory. First, pull the configs file with the `upswebutils` module. The following downloads all artifacts from wxD (search templates, pipelines, and spider configs) into a local directory:

```bash
upswebutils setup backup --instance dev --outdir some/path/configs
```

Next, navigate into the `spider_configs` directory and copy one of the spider configs. Let's assume we want to write a `ups-global` crawler, we can do the following:

```bash
cd some/path/configs/spider_configs
cp ups-com.json ups-global.json
```

Then, modify the `ups-global.json` file to configure `allowed_domains`, `seeds`, `depth`, etc. With that in place, you can run the crawler locally to modify `.env` with the following parameter:

```bash
LOCAL_CONFIGS=true
```

That makes sure that the config is loaded from a local directory. Then, in the `ingest_controller.py` module, point to the new config file at the bottom of the module:

```python
IngestController().ingest('some/path/spider_configs/ups-global.json')
```

Finally, run the `ingest_controller.py` to start the crawl. Running the new crawler locally makes debugging easier.


## (2) Deploy spider config

Once the config is finalized, deploy it to wxD. Use the `upswebutils` module:


```bash
upswebutils setup configs --instance dev --infile some/path/ups-global.json
```

That will push the config file into the `spider-config` index on wxD. To fully deploy the change, make sure to apply this (using the `--instance` flag not only to `dev` but also to `pt`, `performance`, `staging`, `prod-east`, and `prod-central`. 

To test that the configuration was successfully pushed, run your local crawler using a remote spider config. Modify `.env` with the following parameter:

```bash
LOCAL_CONFIGS=false
```

Then, in the `ingest_controller.py` module, refer to the name of the config (as specified in the config's `name` field) at the bottom of the module:

```python
IngestController().ingest('ups-global')
```

When running the crawl, the crawler not use the local `ups-global.json` but the configuration residing on wxD. If no errors appear, you are ready for the next step.


## (3) Create crawler python module

For crawlers to appear in the scrapydweb UI, we need to create a Python module for the new crawler. Go to directory containing the spider modules and copy one:

```bash
cd wxdingest/ingest/deploy/ups_ingest/deploy/spiders
cp ups_com.py ups_global.py
```

Note that the Python file name is not important. It will not appear anywhere in the UI. Once the file is copied simply update the file to point to the newly created config

```python
class GlobalSpider(TemplateSpider):
    name = 'ups-global'
    CONFIG_NAME = 'ups-global'
```

The `name` attribute determines the name that appears in the crawler UI. The `CONFIG_NAME` attribute must be set to the name as specified in the spider config file's `name` attribute.

Once these changes are made, the crawler image must be rebuilt and deployed. Once deployed, it should show up in the list of scrawler (or spiders) and can be started.


## (4) Add schedule crawl

Crawlers run on a schedule. A `Timer Task` was created for each of the existing crawlers. To set up a such a scheduled task, go to `Timer Tasks` and click on the `+` icon. There, you specify the schedule in a manner similar to a cron-job. Once set up, the crawler will run on the specified schedule.

</details>
