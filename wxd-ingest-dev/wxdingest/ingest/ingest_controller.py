
import os
from scrapy.crawler import CrawlerProcess
from scrapy.settings import Settings
from scrapy.utils.project import get_project_settings
import json
from typing import Dict, List
import logging
logger = logging.getLogger(__name__)

from wxdingest.model import SpiderConfig
from wxdingest.ingest.template import TemplateSpider


class IngestController:
    """
    Kicks off the crawler process that is coordinated
    by the scrapy library. It makes sure to load a spider
    (or crawler) that is focused on the specified configuration.
    
    
    TODO: Allow for configuring instance name
    """

    def __init__(self):
        self.CONFIGS_BASEPATH = 'config/spider_configs/'
        self.SETTINGS_BASEPATH = 'config/spider_settings/'

    def list_config_names(self) -> List[str]:
        """
        Utility function to list the names of all the configs
        that have been specified. That helps the user to make
        sure that a valid configuration is specified.
        """
        return [os.fsdecode(file).replace('.json', '') for file in os.listdir(os.fsencode(self.CONFIGS_BASEPATH))]

    def ingest(self, config_name: str):
        """ 
        Loads the various settings and configuration,
        and kicks off the crawler process that crawls
        and indexes documents into Elasticsearch.
        """
        
        # Load scrapy settings
        settings: Settings = self._load_scrapy_settings()

        # Start crawling
        self._start_crawl(settings, config_name)

    def _load_scrapy_settings(self) -> Settings:
        """
        Loads settings.py file that resides in the deploy directory.
        """
        # This is where the settings.py resides that is used both locally
        # and when deployed as a pod.
        settings_file_path:str = 'wxdingest.ingest.deploy.ups_ingest.settings'
        # Force scrapy to pull the settings file from there
        os.environ.setdefault('SCRAPY_SETTINGS_MODULE', settings_file_path)
        # Pull settings file
        settings:Settings = get_project_settings()
        # When running locally, it doesn't recognize the scrapy project
        # path, so we just remove that piece here since it is not relevant
        # to run it locally.
        settings.delete('SPIDER_MODULES')

        return settings
    
    def _load_crawler_config(self, config_path: str) -> SpiderConfig:
        # Load the crawler configuration
        try:
            with open(config_path) as fin:
                return SpiderConfig.from_json(json.load(fin))
        except Exception as e:
            logger.error(f"ERROR - error loading crawler configuration: {e}")
            return None

    def _start_crawl(self, settings: Settings, config_name:str) -> None:
        """
        Once we loaded the settings and config,
        we can start the crawler. This will run for
        a while.
        """
        c = CrawlerProcess(settings)
        c.crawl(TemplateSpider, config_name=config_name, purge="false", force_refresh="false")
        c.start()

if __name__ == '__main__':
    IngestController().ingest('/Users/<USER>/git/configs/spider_configs/test.json')
