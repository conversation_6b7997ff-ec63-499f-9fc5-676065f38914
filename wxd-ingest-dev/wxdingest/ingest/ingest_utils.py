from typing import List, Dict, Tuple, Set
import os 
import logging
logger = logging.getLogger(__name__)

from wxdingest import config

class IngestUtils:
    """
    Manages the loadiing of config informatation needed for ingestion,
    text extraction, etc. This keeps the information in one place and
    ensures we only load it once using a Singelton.
    """
    
    # We don't want to countries and languages to laod
    # ever time this class is used. That's why we make
    # this a Singleton
    _instance = None
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls, *args, **kwargs)
            cls._instance.init()
        return cls._instance

    def init(self):
        
        # Path to all condfigs
        self.CONFIGS_PATH:str = config.get_parameter("CONFIGS_BASEPATH", "config")
        
        # Locale generation
        self.COUNTRIES: Set[str] = []
        self.LANGUAGES: Set[str] = []
        # PDF description
        self.LANGUAGE_NAMES: Dict[str,str] = {}
        self.DIALECTS: Dict[Dict[str,str]] = {}
        # Populate country/language data
        self._load_countries_and_languages()
        
        # PDF prompts
        self.LANGID_PROMPT:str = None
        self.DESCRIPTION_PROMPT:str = None
        self._load_pdf_prompts()
        

    def _load_countries_and_languages(self):
        """
        The list of valid countries and languages are pre-defined
        and will be used to determine whether a key is a match
        to a country/language. Only then, will it be directed to 
        that specific index.
        """
        try:
            # Load country codes
            with open(os.path.join(self.CONFIGS_PATH,'countries.txt')) as fin:
                self.COUNTRIES = set([l.strip() for l in fin])
                
            # Load mapping of language codes to language names
            with open(os.path.join(self.CONFIGS_PATH,'languages.txt')) as fin:
                self.LANGUAGE_NAMES = {l.strip().split(" ")[0]:l.strip().split(" ")[1] for l in fin}
            # ... we also just need a list of language codes
            self.LANGUAGES = set(self.LANGUAGE_NAMES.keys())
            
            
            # Load a mapping of countries to their dialects.
            # This is used for PDF description generation
            with open(os.path.join(self.CONFIGS_PATH,'dialects.txt')) as fin:
                for line in fin:
                    try:
                        # First extract country code
                        details = line.strip().split(" | ")
                        country_code = details[0]
                        if country_code not in self.LANGUAGES:
                            self.DIALECTS[country_code] = {}
                        # Then map all the dialects to that country
                        dialects = details[1:]
                        for dialect in dialects:
                            lang_code, dialect_name = dialect.split(":")
                            self.DIALECTS[country_code][lang_code] = dialect_name
                    except Exception as _:
                        logger.warning(f"Malformed line in country dialect file: '{line.strip()}'")
            logger.info(f"Loaded {len(self.COUNTRIES)} countries and {len(self.LANGUAGES)} languages.")
        except Exception as e:
            logger.error(f"Error loading the countries and languages files: {e}")
            
            
    def _load_pdf_prompts(self):
        """
        Loads the prompts for detecting PDF language and generating
        the PDF description.
        """
        
        try:
            # Load language ID prompt
            with open(os.path.join(self.CONFIGS_PATH,'pdf_langid_prompt.txt')) as fin:
                self.LANGID_PROMPT = fin.read()
                
            # Load description generation prompt
            with open(os.path.join(self.CONFIGS_PATH,'pdf_description_prompt.txt')) as fin:
                self.DESCRIPTION_PROMPT = fin.read()
        except Exception as e:
            logger.error(f"Error loading PDF lang_id and description prompts: {e}")
            
            
    def _load_countries(self):
        """
        Loads the list of countries to be used for locale detection and 
        PDF description generation.
        """
        try:
            
            self.__language_names = dict()
            with open(os.path.join(self.CONFIGS_PATH,'languages.txt')) as fin:
                self.__language_names = {l.strip().split(" ")[0]:l.strip().split(" ")[1] for l in fin}
            
            
            with open(os.path.join(self.CONFIGS_PATH,'countries.txt')) as fin:
                self.COUNTRIES = set([l.strip() for l in fin])
            with open(os.path.join(self.CONFIGS_PATH,'languages.txt')) as fin:
                self.LANGUAGES = set([l.strip() for l in fin])
            logger.info(f"Loaded {len(self.COUNTRIES)} countries and {len(self.LANGUAGES)} languages.")
        except:
            logger.error("Error loading the countries and languages files.")



    def get_country_and_language_from_url_and_locale(self, url: str, locale: str=None) -> Tuple[str, str]:
        """
        Determines the country and language associated with the
        URL so that we can use it to pick the right index and pipeline.

        E.g., URL: ups.com/jp/en --> COUNTRY: jp, LANGAUGE: en
        """
        country: str = None
        language: str = None
        try:

            el_1: str = None
            el_2: str = None

            # Try to parse country/language from locale
            if locale is not None:
                locale_arr: List[str] = locale.split('_')
                try:
                    el_1 = locale_arr[1].lower()
                except Exception as e:
                    pass
                try:
                    el_2 = locale_arr[0].lower()
                except Exception as e:
                    pass

            # Try to parse country and language from URL
            if el_1 is None or el_2 is None:
                url_arr: List[str] = url.split('/')
                try:
                    el_1 = url_arr[3]
                except Exception as e:
                    pass
                try:
                    el_2 = url_arr[4]
                except Exception as e:
                    pass

            # Make sure country and language are valid
            # values and not non-country parts of the URL
            if el_1 in self.COUNTRIES:
                country = el_1
            if el_2 in self.LANGUAGES:
                language = el_2
                
            # logger.info(f'Locale for {url}  --- {el_1}/{el_2} -- {country}/{language}')

            return country, language

        except Exception as e:
            logger.warning(f'Error parsing country and language: {e}')
        # Only returned in exception case
        return None, None
    
    
    def generate_locale(self, country: str, language: str, locale_default:str=None):
        """
        Generates a locale attribute from the discovered country and language.
        """
        if country is None or language is None:
            return locale_default
        else:  # "en_US"
            return f"{language.lower()}_{country.upper()}"
        
        
    # def get_document_locale_from_wxd(self,url:str):
        