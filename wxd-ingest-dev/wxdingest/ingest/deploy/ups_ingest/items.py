# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy


class UpsIngestItem(scrapy.Item):
    # define the fields for your item here like:
    # name = scrapy.Field()

    doc_id = scrapy.Field()
    url = scrapy.Field()
    title = scrapy.Field()
    body = scrapy.Field()
    locale = scrapy.Field()
    description = scrapy.Field()
    keywords = scrapy.Field()
    published_time = scrapy.Field()
    index = scrapy.Field()
    pipeline = scrapy.Field()
    latency = scrapy.Field()
    depth = scrapy.Field()
    referrer = scrapy.Field()
    ingest_duration = scrapy.Field()
    ingest_action = scrapy.Field()
    
    def __repr__(self):
        return f"Item: [{self['ingest_action']}] {self['url']} [{len(self['body'])} chars] {self['latency']:.1f} / {self['ingest_duration']:.1f} seconds latency at depth {self['depth']} -- referrer: {self['referrer']}"