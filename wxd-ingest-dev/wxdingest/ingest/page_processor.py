from typing import List, Tuple, Set, Any
import hashlib
from datetime import datetime, timedelta
from time import time
import logging
logger = logging.getLogger(__name__)

from wxdingest.elasticsearch_interface import ElasticsearchInterface
from wxdingest.setup.instances_manager import InstancesManager
from wxdingest.setup.setup_controller import SetupController
from wxdingest.model import IngestDocument
from wxdingest.ingest.html_extractor import HtmlExtractor
from wxdingest.ingest.pdf_extractor import PdfExtractor
from wxdingest.ingest.ingest_utils import IngestUtils
import os

class PageProcessor:

    def __init__(self, *, instance_name:str=None):
        self.PDF_INDEX:bool = os.environ.get('PDF_INDEX',"True").lower() in ['true', '1']
        self.instance_name:str = instance_name
        self.elastic:ElasticsearchInterface = InstancesManager().wxd(instance_name)
        self.setup = SetupController(instance_name=instance_name)
        self.utils = IngestUtils()
        self.indices: List[str] = self.elastic.get_indices()

    def process_page(self, response, index: str, pipeline: str, locale_default: str, *, log=None, force_refresh:bool=False):
        """
        Whenever a new page is discovered by the spider, this processes that
        page by parsing it, determining index/pipeline, and sending it
        to wxD for processing and indexing.
        """
        start_time = time()
        
        if response.url.endswith("pdf"):
            # We can put PDFs into a separate index to keep them away from web pages
            # That makes sure PDFs don't have a negative impact on Search
            if self.PDF_INDEX:
                index += "-pdf"
            doc = PdfExtractor(instance_name=self.instance_name).extract(response,index)
        else:
            doc = HtmlExtractor().extract(response)

        # Exit if there was a failure in extraction            
        if doc is None:
            return None
            
        # Add crawling metadata
        doc.latency = response.meta.get('download_latency')
        doc.depth = response.meta.get('depth')
        try:
            doc.referrer = response.request.headers.get('Referer', "N/A").decode("utf-8")
        except:
            pass
        doc.ingest_time = datetime.utcnow()

        # The hash is used to check for modifications
        doc.hash = self._hash_document(doc)
        # Calculate document size so that we can add it to the index
        doc.size = self._get_text_size(doc.body)

        # Since we generate the index name based on country
        # and language, we need to make sure that index already
        # exists
        self._check_index(index)

        # Send the document to wxd
        response = self._ingest_document(doc, index, pipeline, force_refresh=force_refresh)
        logger.debug(f"wxD response: [{response.meta.status}] {response.body['result']} for {doc.url}")
        if response.meta.status not in [200,201]:
            logger.error(f"Error ingesting document: {doc.url}")
            
        doc.ingest_duration = time() - start_time
        return doc
    
    
    def _ingest_document(self, doc:IngestDocument, index, pipeline,*,force_refresh:bool=False):
        
        if force_refresh:
            # In some cases, we might want the document updated regardless of its hash
            response = self.elastic.ingest_document(doc.to_json(), doc.id, index, pipeline)
            doc.ingest_action = "UPDATE"
        else:
            # Get document by ID
            jdoc:Any = None
            try:
                jdoc = self.elastic.get_document_by_id(doc.id, index)
            except:
                pass
            if jdoc is None:
                response = self.elastic.ingest_document(doc.to_json(), doc.id, index, pipeline)
                doc.ingest_action = "CREATE"
            else:
                # Decide on processing action based on document response
                doc_hash:str = jdoc.get("hash")
                if doc_hash != doc.hash:
                    response = self.elastic.ingest_document(doc.to_json(), doc.id, index, pipeline)
                    doc.ingest_action = "UPDATE"
                else:
                    response = self.elastic.update_document_date(doc.id, index)
                    doc.ingest_action = "DATE"
        return response
    

    def _hash_document(self, doc:IngestDocument, *, algorithm='sha256') -> str:
        """
        Hashes the document text. This is used to identify if a document has changed.
        """
        texts_to_hash:List[str] = [doc.body, doc.title, doc.description]
        hasher = hashlib.new(algorithm)
        hasher.update("|".join([str(t) for t in texts_to_hash]).encode('utf-8'))
        return hasher.hexdigest()
    
    def _get_text_size(self, text:str) -> int:
        """
        Calculates the size of the text. Currently just uses the character count
        but could use token count also.
        """
        if text is None:
            return 0
        try:
            return len(text)
        except Exception as e:
            logger.warning(f"Failed to calculate document size: {e}")
            return 0

    def _check_index(self, index_name: str, index_mapping_name: str='english'):
        """
        Checks if the index exists and, if not,
        creates a new index. Note that if we create the index
        with a non-english mapping and then push english documents into it,
        we get a 400 error. So, we need to create all indices with an "english"
        index mapping.
        """
        if not index_name in self.indices:
            logger.info(
                f'Current index list is: {self.indices}\n\nCreating index {index_name} with mapping {index_mapping_name}')
            try:
                self.setup.create_index(
                    index_name=index_name, index_mapping=index_mapping_name)
                
                # Also create an empy suggest document
                self.setup.create_suggest_doc(index_name)
            except Exception as e:
                logger.error(f"Error when creating index: {e}")
            self.indices.append(index_name)


    def _generate_index(self, base_index: str, *, country: str = None, language: str = None):
        """
        Generates a sub-index based on the base index,
        the country, and the language, specified in the URL.
        """
        if language:
            if country:
                # If both language and country are specified, we split
                # indices for each language/country permutation
                return f'{base_index}-{country}-{language}'
            else:
                # If only language is specified, we split by
                # english and non-english
                if language == 'en':
                    return f'{base_index}-english'
                else:
                    return f'{base_index}-non-english'
                return
        else:
            return base_index


    def _generate_pipeline(self, language: str):
        """
        Generates the pipeline name only differnetianting
        between english and non-english at the moment.
        """
        if language == 'en':
            return 'english'
        else:
            return 'non-english'
        
    
    def purge_old_pages(self, crawl_start_time:datetime, index:str, *, stale_days:int=0, purge:bool=False):
        """
        Purges documents that should be deleted
        """
        
        # Make sure we have an index or this function will fail
        # if no documents have been ingested.
        self._check_index(index)
        
        # We might not want to delete all documents that were not ingested in the last
        # ingest but let them live if they are not older than n days.
        if stale_days > 0:
            crawl_start_time = crawl_start_time - timedelta(days=stale_days)
            logger.info(f"Pages older than {stale_days} days ({crawl_start_time.isoformat()}) should be removed.")
            
        # First, let the user know what would be deleted
        query = {"query": {"range": {"ingest_time": {"lt": crawl_start_time}}}, "size":300}
        response = self.elastic.query(index,query)
        total_cnt:int = response["hits"]["total"]["value"]
        hit_list:str = "\n\t".join([hit['_source']['url'] for hit in response["hits"]["hits"]])
        if purge:
            logger.info(f"{total_cnt} pages are stale and will be deleted:\n\t{hit_list}")
        else:
            logger.info(f"{total_cnt} pages will NOT be deleted but are stale:\n\t{hit_list}")
            
        
        # Then go ahead and delete
        if purge:
            response = self.elastic.delete_old_documents(crawl_start_time, index)
            logger.info(f"Purged {response.get('deleted')} pages due to staleness.")
