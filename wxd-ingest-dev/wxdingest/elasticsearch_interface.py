
import collections
from typing import Mapping, Any, List
import copy
from retry import retry
from elasticsearch import Elasticsearch, helpers
from elastic_transport import ConnectionError
from typing import Union, Dict, Optional
from datetime import datetime

from wxdingest.utils.applog import get_logger
logger = get_logger(__name__)

from wxdingest import config
from wxdingest.utils.timing import Timing

import logging
logging.getLogger('elastic_transport.transport').setLevel(logging.CRITICAL)
logging.getLogger('urllib3.connectionpool').setLevel(logging.CRITICAL)
        

class ElasticsearchInterface():
    """
    Handles low level interactions with Elasticsearch
    """
    def __init__(
        self,
        *,
        url:str=None, 
        user:str=None, 
        password:str=None
    ):
        # super().__init__(config=config)
        
        # Setup elasticsearch configuration
        self._es_config = dict()
            
        if url is not None and user is not None and password is not None:
            self.ELASTICSEARCH_URL = url
            self.ELASTICSEARCH_USER_NAME = user
            self.ELASTICSEARCH_PASSWORD = password
        else:
            self.ELASTICSEARCH_URL = config.get_parameter('ELASTICSEARCH_URL')
            self.ELASTICSEARCH_USER_NAME = config.get_parameter('ELASTICSEARCH_USER_NAME')
            self.ELASTICSEARCH_PASSWORD = config.get_parameter('ELASTICSEARCH_PASSWORD')
        
        self.timer = Timing()
        
        # Initiate elasticsearch client
        self.set_client()
        
    def set_client(self, *, timeout:int=60, retries:int=3):
        """
        Initiate Elasticsearch client
        """
        self.client = Elasticsearch(
            self.ELASTICSEARCH_URL,
            verify_certs=False,            
            basic_auth=(
                self.ELASTICSEARCH_USER_NAME, 
                self.ELASTICSEARCH_PASSWORD
            ),
            ssl_show_warn=False,
            timeout=timeout,
            max_retries=retries
        )
    
    def close_connection(self):
        """
        For closing connection
        """
        try:
            if self.client != None:
                self.client.transport.close()
                logger.info('Connection closed')

        except Exception as e:
            logger.error(f"Exception occurred while closing the connection as {e}")

        
    def has_index(self, index: str, **kwargs):
        """
        Check if the index exists
        """
        return self.client.indices.exists(index=index, **kwargs).body
    
    def create_index(
        self,
        index:str,
        index_config: Dict
    ):
        """
        Create an index
        """
        return self.client.indices.create(index=index,body=index_config)
    
    
    @retry(tries=3, exceptions=ConnectionError, logger=get_logger(__name__))
    def ingest_document(
        self,
        document: Mapping[str, Any],
        id: str  = None,
        index: str  = None,
        pipeline: str  = None,
        **kwargs
    ):
        """
        Ingests one document
        """
        
        
        # Use default index if not provided
        res = self.client.index(
            index=index,
            document=document,
            id=id,
            pipeline=pipeline,
            **kwargs
        )
        _ = self.client.indices.refresh(index=index)
        return res
        
    
    @retry(tries=3, exceptions=ConnectionError, logger=get_logger(__name__))
    def bulk_upload(
        self,
        index: str,
        documents:List[any],
        pipeline
    ):
        """
        Upload in bulk
        """
        
        logging.getLogger('elastic_transport.transport').setLevel(logging.CRITICAL)

        try:
            response =  helpers.bulk(
                self.client,
                documents,
                index=index,
                pipeline=pipeline
            )
        except Exception as e:
            try:
                print('\n\n-------------------------\nERROR in bulk indexing: {}\n-------------------------\n\n'.format(e.errors[0]['index']['error']['reason']))
            except:
                print('\n\n-------------------------')
                print(e)
                print('-------------------------\n\n')
            return None
    
    def delete_index(
        self,
        index: str,
        **kwargs
    ):
        """
        Delete an index
        """
        return self.client.indices.delete(index=index, **kwargs)
    
    
    @retry(tries=3, exceptions=ConnectionError, logger=get_logger(__name__))
    def query(self, index:str, query_body: Dict, **kwargs ):
        """
        Query
        """
        
        # Search
        try:
            res = self.client.search(
                index=index,
                **query_body
            ).body
        except Exception as e:
            self.set_client()
            raise e
        
        return res
        
        
    def create_pipeline(self, name, config):
        self.client.ingest.put_pipeline(id=name,body=config)


    def get_pipelines(self,*,filter:List[str]=[]):
        """
        Lists all the pipelines already in elastic.
        The filter allows us to only return specific pipelines
        so that we don't get too much noise. This helps to check
        if the pipelines we created are already specified.
        """
        try:
            return self.client.ingest.get_pipeline(id=','.join(filter),pretty=True)
        except Exception as e:
            logger.error(f"Exception occurred while retrieving pipelines: {e}")
            return []
        
        
        
    def get_indices(self, *, index_name:str="*") -> List[str]:
        """
        Returns the name of all the indices.
        """
        try:
            return [i for i in self.client.indices.get_alias(index=index_name)]
        except Exception as e:
            logger.info(f"Index does not seem to exist yet: {e}")
            return []
        
        
    def get_document_by_id(self, doc_id:str, index:str, *, fields:List[str]=["hash"]) -> any:
        # Retrieve the document
        document = self.client.get(index=index, id=doc_id, _source_includes=fields)
        
        # Access the document source (data)
        source_data = document.get('_source',{})#.get("published_time")
        return source_data
        
    def update_document_date(self, doc_id:str, index:str):
        """
        For documents that have not been modified, we simply
        set the ingest_time to the current time. Documents that 
        are not updated with a new ingest_time, will be purged
        after the ingest process is completed.
        """
        
        update_body = {
            'doc': {
                'ingest_time': datetime.utcnow()
            }
        }

        # Execute the update request
        response = self.client.update(index=index, id=doc_id, body=update_body)
        return response
    
    
    def delete_old_documents(self, start_time:datetime, index:str):
        """
        Deletes documents that were ingested before the current
        run and not updated.
        """
        
        # If we don't refresh, we get a 409 error from elastic
        _ = self.client.indices.refresh(index=index)
        
        # Formulate the delete query
        query = {
            "query": {
                "range": {
                    "ingest_time": {
                        "lt": start_time
                    }
                }
            }
        }
        # ... and execute that query
        response = self.client.delete_by_query(index=index, body=query)
        return response

if __name__ == '__main__':
    i = ElasticsearchInterface().get_indices()
    print(i)