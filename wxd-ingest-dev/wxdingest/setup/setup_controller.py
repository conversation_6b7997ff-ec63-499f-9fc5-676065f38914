
import os
from typing import List
import json

from wxdingest.elasticsearch_interface import ElasticsearchInterface
from wxdingest.setup.instances_manager import InstancesManager
from wxdingest.utils.timing import Timing
from wxdingest.model import SearchResultDocument, SearchResultPassage, IngestDocument, MasterConfig
from wxdingest.utils import applog
logger = applog.get_logger()
from wxdingest import config


class SetupController:
    
    def __init__(self, *, instance_name:str=None):
        self.elastic:ElasticsearchInterface = InstancesManager().wxd(instance_name)
        self.CONFIGS_PATH:str = config.get_parameter("CONFIGS_BASEPATH", "config")
        self.PIPELINES_PATH = self._make_abs_config_path('pipelines', base_path=self.CONFIGS_PATH)
        self.PASSAGE_SPLITTER_PATH = self._make_abs_config_path('pipelines/chunker.painless', base_path=self.CONFIGS_PATH)
        self.INDICES_PATH = self._make_abs_config_path('indices', base_path=self.CONFIGS_PATH)
        self.timing = Timing()
        
    def _make_abs_config_path(self, dir:str, base_path:str=None) -> str:
        """
        When running the spider from scrapy, we need to be able to resolve the
        config directory.
        """
        if base_path is None:
            # For local testing we can find the config directory relative to this file
            return os.path.abspath(os.path.join(os.path.dirname( __file__ ), '..','..','config', dir))
        else:
            # For pod deployments, we need to find the configs in the specified directory
            return os.path.abspath(os.path.join(base_path, dir))

    def list_pipeline_config_names(self) -> List[str]:
        fnames = [os.fsdecode(file) for file in os.listdir(
            os.fsencode(self.PIPELINES_PATH))]
        fnames = [f.replace('_pipeline.json', '')
                  for f in fnames if f.endswith('.json')]
        return fnames

    def list_pipelines(self) -> List[str]:
        """
        Utility function to list the names of all the configs
        that have been specified. It also finds if these pipelines
        have already been created in ES.
        """

        # Read the pipeline config names
        pipeline_specs: List[str] = self.list_pipeline_config_names()

        # Retrieve pipeline list from ES
        pipelines: List[str] = [
            p for p in self.elastic.get_pipelines(filter=pipeline_specs).body]

        # Create a list that marks the created pipelines as already created
        plist: List[str] = []
        for pspec in pipeline_specs:
            if pspec in pipelines:
                plist.append(pspec + " (already created)")
            else:
                plist.append(pspec)
        return plist

    def create_pipelines(self, *, pipeline_name:str=None, indir:str=None) -> None:
        """
        Creates all the pipelines defined in the pipelines folder
        in Elasticsearch. Only creates a specific pipeline if a 
        pipeline_name is specified.
        TODO Search pipeline specs in indir
        """
        for fname in [f for f in self._get_files_from_directory(self.PIPELINES_PATH) if f.endswith('.json')]:
            fpipeline_name = fname.split('.json')[0]
            if pipeline_name is None or fpipeline_name == pipeline_name:

                with open(os.path.abspath(os.path.join(self.PIPELINES_PATH, fname)), 'r') as fin:
                    jpipln = json.load(fin)
                    # If the pipeline has a "script" node, with the description attribute
                    # set to "Passage Splitter", we need to insert the painless script for
                    # passage splitting
                    if jpipln.get('processors', [{}])[0].get('script', {}).get('description', '').startswith('Passage Splitter'):
                        # Insert passage splitter code
                        with open(os.path.abspath(self.PASSAGE_SPLITTER_PATH), 'r') as ps_fin:
                            try:
                                script_obj: any = jpipln.get('processors', [{}])[
                                    0].get('script', {})
                                script_obj['source'] = ps_fin.read()
                            except:
                                pass
                    if fpipeline_name == "enrich":
                        # Insert passage splitter code
                        with open(os.path.abspath(os.path.join(self.PIPELINES_PATH, fpipeline_name+'.painless')), 'r') as ps_fin:
                            try:
                                script_obj: any = jpipln.get('processors', [{}])[
                                    1].get('script', {})
                                script_obj['source'] = ps_fin.read()
                            except:
                                pass

                    self.elastic.create_pipeline(fpipeline_name, jpipln)
                    print(f'Created pipeline {fpipeline_name} ')

    def create_index(self, index_name: str, index_mapping: str, *, delete_existing_index: bool = False) -> List[str]:
        """
        Creates an index with the given name and the specified
        mapping. For the mapping, we really only distinguish between
        english and non-english (if at all).
        """
        for fname in self._get_files_from_directory(self.INDICES_PATH):
            fimapping = fname.split('_')[0]
            fimapping = fimapping.replace(".json", "")
            if fimapping == index_mapping:

                # First delete the existing index, if so desired
                if delete_existing_index:
                    try:
                        self.elastic.delete_index(index_name)
                    except Exception as e:
                        print(f'Error deleting index {index_name}: {e}')

                # Load the index mapping to be applied and create index
                with open(os.path.abspath(os.path.join(self.INDICES_PATH, fname)), 'r') as fin:
                    jidx = json.load(fin)
                    self.elastic.create_index(index_name, jidx)
                    print(f'Created index {index_name}')
                    return

        print(
            f"ERROR: Could not find index config for index '{index_name}' and '{index_mapping}' mapping")

    def _get_files_from_directory(self, dpath: str):
        """
        Helper function to find all the files in a specific directory.
        """
        logger.info(f'Indices config path: {dpath}')
        return [os.fsdecode(file) for file in os.listdir(dpath)]
    
    def print_health_status(self):
        """
        Visits all instances and determines the health of the system.
        """
        instances:List[ElasticsearchInterface] = InstancesManager().get_all_wxd()
        if len(instances)<=0:
            print("No instance list defined.")
            return
        
        print(f"\n--------------------------------------------------")
        print(f"Health status for {len(instances)} instances")
        for instance in instances:
            r = instance.client.health_report()
            print(f"\tStatus for {r.body['cluster_name']} ({r.meta.node.host}) is {r.body['status']}")
            
            
    def create_suggest_doc(self, index_name:str):
        """
        If an index does not have a suggest field for a specific locale, it will cause an error.
        This makes sure that there is at least one suggest field for every locale. It does that
        by creating a document that specifies an empty suggest field for each locale. That document
        needs to be updates to make sure that it does not get deleted.
        """
        with open(os.path.join(self.CONFIGS_PATH,'locales.txt')) as fin:
            # Load locales from config file
            locales:Set[str] = set([l.strip() for l in fin])
            # Build a simple document
            suggest_doc:any = {f"suggest_{l}":"" for l in locales}
            # Index that document
            self.elastic.ingest_document(suggest_doc, "suggest_locales", index_name)
            
    def push_suggest_doc(self):
        indices = []
        

if __name__ == '__main__':
    # SetupController().print_health_status()
    # SetupController().create_suggest_doc("test-chris-01")
    SetupController(instance_name="dev").create_pipelines()