from typing import List
import os
import json
import logging
logger = logging.getLogger(__name__)

from wxdingest.model import SpiderConfig
from wxdingest import config
from wxdingest.setup.instances_manager import InstancesManager
from wxdingest.elasticsearch_interface import ElasticsearchInterface
from wxdingest.setup.setup_controller import SetupController

class SpiderConfigManager:
    
    def __init__(self, *, instance_name:str=None):
        self.SPIDER_CONFIGS_INDEX = "spider-configs"
        self.elastic:ElasticsearchInterface = InstancesManager().wxd(instance_name)
        self.setup = SetupController()
        self.index_name:str = "spider-configs"
        self.index_mapping_name:str = "spider-configs"
        self.config_path:str = ""
        self.load_from_local:bool = config.get_parameter('LOCAL_CONFIGS', False)
        
    def upload_configs_to_wxd(self, configs_dir:str, *, config_name:str=None) -> None:
        """
        Uploads the crawler configurations to wxd. This should only
        be done to initialze the spider_config index on wxD.
        """
        # Make sure index exists
        self._check_index()
        # Load files from directory
        spider_configs:List[SpiderConfig] = self._load_configs_from_directory(configs_dir)
        logger.info(f"Found {len(spider_configs)} spider configurations")
        for sconfig in spider_configs:
            if config_name is None or sconfig.name == config_name:
                self.elastic.ingest_document(sconfig.to_json(), id=sconfig.name, index=self.index_name)
        logger.info("Uploaded spider configurations to wxd")
    
    def _load_configs_from_directory(self, configs_dir:str) -> List[SpiderConfig]:
        """
        Loads configurations from the local directory.
        """
        spider_configs:List[SpiderConfig] = []
        for root, dirs, files in os.walk(configs_dir):
            for file in files:
                if file.endswith('.json'):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r') as fin:
                        try:
                            spider_configs.append(SpiderConfig.from_json(json.load(fin)))
                        except json.JSONDecodeError as e:
                            logger.error(f"Error decoding JSON from file {file_path}: {e}")
        return spider_configs
        
    def _check_index(self):
        """
        Checks if the index exists and, if not,
        creates a new index.
        """
        
        indices: List[str] = self.elastic.get_indices(index_name=self.index_name)
        
        if len(indices) <=0 :
            logger.info(
                f'Creating index {self.index_name} with mapping {self.index_mapping_name}')
            try:
                self.setup.create_index(
                    index_name=self.index_name, index_mapping=self.index_mapping_name)
            except Exception as e:
                logger.error(f"Error when creating index: {e}")

    def save_spider_config_to_file(self, config_name:str, out_dir:str) -> None:
        """
        Saves the specified spider configuration to a file. That allows
        for modifying that configuration and uploading them back
        up to wxD.
        """
        config:SpiderConfig = self.load_spider_config(config_name)
        if config is not None:
            out_path = os.path.join(out_dir, f"{config_name}.json")
            with open(out_path, 'w') as fout:
                json.dump(config.to_json(), fout, indent=2)
            logger.info(f"Saved spider configuration to {out_path}")
            
    def upload_spider_config_from_file(self, config_path:str) -> None:
        """
        Loads the specified JSON file and updates the spider config
        with that name in wxD
        """
        with open(config_path, 'r') as fin:
            config = SpiderConfig.from_json(json.load(fin))
            self.elastic.ingest_document(config.to_json(), id=config.name, index=self.index_name)
            logger.info(f"Uploaded spider configuration {config.name} to wxD")


    def load_spider_config(self, config_name:str) -> SpiderConfig:
        """
        Loads the crawler config to apply them to the spider.
        """
        if self.load_from_local:
            logger.info(f"Loading spider config '{config_name}' from local file system.")
            return self._load_spider_config_from_local(config_name)
        else:
            logger.info(f"Loading spider config '{config_name}' from spider_configs index in wxD.")
            return self._download_spider_config_from_wxd(config_name)
        
        
    def _download_spider_config_from_wxd(self, config_name:str) -> SpiderConfig:
        """
        Loads the crawler config from wxD
        """
        
        wxd_query:any = {"query": { "match": { "name" : config_name}}}
        response = self.elastic.query(query_body=wxd_query, index=self.index_name)
        
        try:
            for hit in response.get('hits', {}).get('hits', []):
                return SpiderConfig.from_json(hit.get('_source'))
        except Exception as e:
            logger.error(f"Error loading crawler configuration from wxD: {e}")
            return None
        
    def download_all_spider_config_from_wxd(self, output_dir:str) -> None:
        """
        Loads the crawler config from wxD
        """
        
        wxd_query:any = {"query": { "match_all": {}}, "size": 100}
        response = self.elastic.query(query_body=wxd_query, index=self.index_name)
        
        try:
            for hit in response.get('hits', {}).get('hits', []):
                fpath:str = os.path.abspath(os.path.join(output_dir,hit["_source"]["name"]+".json"))
                print(f"Downloading spider config {hit['_source']['name']} to {fpath}")
                with open(fpath,'w') as fout:
                    json.dump(hit["_source"],fout, indent=2)
            print(f"Downloaded {len(response.get('hits', {}).get('hits', []))} spider configs to {output_dir}")
        except Exception as e:
            print(f"Error loading crawler configuration from wxD: {e}")
            return None

    def _load_spider_config_from_local(self, config_path:str) -> SpiderConfig:
        """
        Loads the crawler config to apply them to the spider.
        """
        
        # Make sure the specified crawler configuration exists
        if not os.path.exists(config_path):
            logger.error(f'ERROR - crawler config in {config_path} does not exist')
            return None

        # Load the crawler configuration
        try:
            with open(config_path) as fin:
                return SpiderConfig.from_json(json.load(fin))
        except Exception as e:
            logger.error(f"ERROR - error loading crawler configuration: {e}")
            return None
        
if __name__ == "__main__":
    # config = SpiderConfigManager().load_spider_config('ups_com')
    SpiderConfigManager(instance_name="prod-central").upload_configs_to_wxd("/Users/<USER>/git/configs/spider_configs")
    # SpiderConfigManager().upload_spider_config_from_file("/Users/<USER>/temp/UPS/crawl.json")
    
    # SpiderConfigManager().download_all_spider_config_from_wxd("/Users/<USER>/temp/UPS/config_dump/spider_configs")