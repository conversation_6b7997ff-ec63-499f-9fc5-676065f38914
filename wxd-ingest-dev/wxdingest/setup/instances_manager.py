"""
Manages the credentials for the different instances. It allows
specifying the target instance as paramater and then let this 
class deal with providing the appropriate credentials.

This assumes that the credentials have been defined in a JSON 
file and the environment variable "INSTANCES" points to that file.
"""


from typing import List,Dict
import json
from wxdingest import config
from wxdingest.elasticsearch_interface import ElasticsearchInterface

class WxDInstance:
    url:str = None
    user:str = None
    password:str = None
    
    def __init__(self, *, name:str=None, url:str=None, user:str=None, password:str=None):
        self.name = name
        self.url = url
        self.user = user
        self.password = password
        self.interface:ElasticsearchInterface = None
    
    def wxd(self):
        if self.interface is None:
            self.interface = ElasticsearchInterface(url=self.url, user=self.user, password=self.password)
        return self.interface

class InstancesManager:
    
    def __init__(self):
        self.default:WxDInstance = self._load_default()
        self.instances = self._load_instances()
        
    def _load_default(self):
        default:WxDInstance = WxDInstance()
        default.name = "default"
        default.url = config.get_parameter("ELASTICSEARCH_URL")
        default.user = config.get_parameter("ELASTICSEARCH_USER_NAME")
        default.password = config.get_parameter("ELASTICSEARCH_PASSWORD")
        return default
        
    def _load_instances(self):
        instances_path:str = config.get_parameter("INSTANCES")
        if instances_path is None:
            return None
        else:
            try:
                with open(instances_path) as fin:
                    instances:Dict[str,WxDInstance] = {val["name"]:WxDInstance(name=val["name"], url=val["url"], user=val.get("user","elastic"), password=val["key"]) for val in json.load(fin)}
                    return instances
            except Exception as e:
                print(f"Error loading instances: {e}")
                return None
    
    def wxd(self, instance_name:str=None) -> ElasticsearchInterface:
        """
        Returns the ElasticsearchInterface for the specific instance.
        """
        if self.instances is None or instance_name not in self.instances:
            return self.default.wxd()
        else:
            return self.instances[instance_name].wxd()
        
    def get_all_wxd(self) -> List[ElasticsearchInterface]:
        """
        Returns ElasticsearchInterface objects for all specified instances.
        This is used to visit all instances to conduct health checks etc.
        """
        if self.instances is None:
            return [self.default.wxd()]
        else:
            return [i.wxd() for i in self.instances.values()]
        
    def print_instance_names(self):
        if len(self.instances)<=0:
            print("No instances have been specified.")
        else:
            print(f"{len(self.instances)} instances have been specified: " + ",".join(self.instances.keys()))
        
if __name__ == "__main__":
    InstancesManager().print_instance_names()

    