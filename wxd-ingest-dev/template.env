

# ELA<PERSON><PERSON><PERSON><PERSON>CH INSTANCES
# The ingest code that is run in the Docker container
# will always refer to the default instance that is configured 
# in environment variables. This will also be the default if no
# instance name is provided or if no instances.json exists.

# DEV - Your WatsonX Discovery Instance
ELASTICSEARCH_URL=https://4aabc8d0-f685-4614-8d38-3e1cda998e63.blijtlfd05jdimoomdig.databases.appdomain.cloud:32474
ELASTICSEARCH_USER_NAME=ibm_cloud_41621667_430d_459f_908f_6f5aeca57ead
ELASTICSEARCH_PASSWORD=rXs8FjIXT9KFmGrulPdmiUrLM8kBT9mO

# Path to instances.json that holds credentials for all environments (optional for local testing)
# INSTANCES=/path/to/instances.json


# ELASTICSEARCH BASE CONFIGURATION
# This defines the name of the ML models and fields
# as they are used when running searches. It needs
# to match the name of the models as deployed ini ES.

# Model names
E5_MODEL_NAME=.multilingual-e5-small_linux-x86_64
E5_FIELD_NAME=e5.predicted_value

# Field names
ELSER_MODEL_NAME=.elser_model_2_linux-x86_64
ELSER_FIELD_NAME=elser.predicted_value


# INGESTION PARAMETERS
# Parameters used during ingestion

# Control the purging of documents as described in the README
STALE_DAYS=10
PURGE=true


# DEVELOPMENT AND TESTING
# This sets paths to various directories needed to 
# perform various actions.

# Used by ingest and setup to find configuration artifacts,
# such as language files, pipelines, and index mappings.
# This is also used by the container to find configuration files.
CONFIGS_BASEPATH=/Users/<USER>/Desktop/Lumen/code_existing/wxd-ingest-dev/config

# When we ingest, we pull spider configs from the Elasticsearch
# spider-configs index. But for testing, we might want to use a local
# config file. This allows us to do that.
LOCAL_CONFIGS=true