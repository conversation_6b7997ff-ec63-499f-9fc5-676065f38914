# coding: utf-8
import os

from setuptools import find_packages, setup

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

about = {}
with open(os.path.join(CURRENT_DIR, 'wxdingest', '__version__.py')) as f:
    exec(f.read(), about)
    
setup(
    name=about['__title__'],
    version=about['__version__'],
    author=about['__author__'],
    url=about['__url__'],
    description=about['__description__'],

    packages=find_packages(),
    include_package_data=True,
    zip_safe=False,
    python_requires=">=3.11",
    install_requires=[
        "elasticsearch==8.15.1",
        "python-dotenv==1.0.1",
        "retry==0.9.2",
        "beautifulsoup4==4.13.3",
        "markdownify==0.14.1",
        "Scrapy==2.12.0",
        "html2text==2024.2.26",
        "pypdf==5.6.0",
        "langdetect==1.0.9",
        "py3langid==0.3.0"
    ],
    
    entry_points={
        "console_scripts": {
            "wxdingest = wxdingest.cli:main"
        }
    },

    classifiers=[
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Python :: 3.13",
    ]
)
